<!DOCTYPE html>
<html>
<head>
  <base target="_top">
  <title>CDR Application Form</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* General styles */
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f2f5;
      color: #333;
      display: flex;
      flex-direction: column;
      width: 100vw; /* Full viewport width */
      height: 100vh; /* Full viewport height */
      overflow-y: auto; /* Allow scrolling for long forms */
      box-sizing: border-box;
    }

    h1 {
      color: #1a73e8;
      text-align: center;
      margin-bottom: 30px;
    }

    /* Form Container */
    .form-container {
      max-width: 900px;
      margin: 0 auto;
      background-color: #ffffff;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      padding: 30px;
      box-sizing: border-box;
      flex-grow: 1; /* Allow container to grow and fill space */
    }

    /* Section Cards */
    .section-card {
        background-color: #fdfdfd; /* Slightly off-white for sections */
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .section-card h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1a73e8;
        margin-bottom: 20px;
        border-bottom: 2px solid #e0e0e0;
        padding-bottom: 10px;
    }

    /* Form Grid Layout */
    .form-grid-layout {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px 30px; /* Row and column gap */
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-size: 0.95rem;
      color: #555;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .form-group input[type="text"],
    .form-group input[type="date"],
    .form-group select,
    .form-group textarea {
      padding: 12px;
      border: 1px solid #ccc;
      border-radius: 8px;
      font-size: 1rem;
      color: #333;
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      border-color: #1a73e8;
      box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.2);
      outline: none;
    }

    .form-group textarea {
      resize: vertical;
      min-height: 80px;
    }

    /* Error messages */
    .error-message {
      color: #ea4335; /* Google Red */
      font-size: 0.8rem;
      margin-top: 5px;
      display: none; /* Hidden by default */
    }

    input:invalid:not(:placeholder-shown),
    textarea:invalid:not(:placeholder-shown) {
      border-color: #ea4335;
    }

    /* Dynamic Fields (Accused, Phone/IMEI) */
    .dynamic-field-container {
      margin-top: 15px; /* Space above dynamic fields */
    }

    .dynamic-input-wrapper {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px; /* Space between dynamic inputs */
    }

    .dynamic-input-wrapper input {
      flex-grow: 1;
    }

    .btn-add, .btn-remove {
      background-color: #34a853; /* Green for add */
      color: white;
      padding: 8px 12px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 0.85rem;
      transition: background-color 0.3s ease;
    }

    .btn-remove {
      background-color: #ea4335; /* Red for remove */
    }

    .btn-add:hover { background-color: #2b8e45; }
    .btn-remove:hover { background-color: #c93427; }

    /* Checkbox Group (Request Type) */
    .checkbox-group {
      /* Already spans full width due to form-grid-layout */
      margin-bottom: 20px;
    }

    .checkbox-group label {
      font-size: 0.95rem;
      color: #555;
      margin-bottom: 10px;
      font-weight: 500;
      display: block;
    }

    .checkbox-options {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-top: 10px;
    }

    .checkbox-option {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .checkbox-option input[type="checkbox"] {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      width: 20px;
      height: 20px;
      border: 2px solid #ccc;
      border-radius: 4px;
      cursor: pointer;
      position: relative;
      outline: none;
      transition: border-color 0.2s ease, background-color 0.2s ease;
    }

    .checkbox-option input[type="checkbox"]:checked {
      background-color: #1a73e8;
      border-color: #1a73e8;
    }

    .checkbox-option input[type="checkbox"]:checked::after {
      content: '✔'; /* Checkmark */
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 14px;
      font-weight: bold;
    }

    .checkbox-option input[type="checkbox"]:disabled {
      background-color: #e0e0e0;
      cursor: not-allowed;
      border-color: #bbb;
    }

    /* File Upload with Preview */
    .file-upload-group {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 20px;
    }

    .file-upload-group label {
      margin-bottom: 10px;
    }

    .file-input-wrapper {
      display: flex;
      align-items: center;
      gap: 15px;
      width: 100%;
    }

    /* Hide native file input */
    .file-input-wrapper input[type="file"] {
      display: none;
    }

    /* Style the custom file input button */
    .custom-file-upload-button {
      background-color: #4285f4; /* Google Blue */
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 0.95rem;
      font-weight: 500;
      transition: background-color 0.3s ease, transform 0.2s ease;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      display: inline-block; /* Make it behave like a button */
      text-align: center;
    }

    .custom-file-upload-button:hover {
      background-color: #3367d6;
      transform: translateY(-1px);
    }

    .file-name-display {
        margin-left: 10px;
        font-size: 0.9rem;
        color: #666;
        flex-grow: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .image-preview {
      width: 120px;
      height: 120px;
      border: 1px solid #ddd;
      border-radius: 8px;
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      margin-top: 10px;
    }

    .image-preview img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }

    .image-preview span {
      color: #999;
      font-size: 0.9rem;
      text-align: center;
    }

    /* Action Buttons */
    .form-actions {
      display: flex;
      justify-content: flex-end; /* Align to the right */
      gap: 15px;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }

    .btn {
      display: inline-block;
      font-family: inherit;
      font-size: 1rem;
      font-weight: 500;
      padding: 10px 24px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: background 0.2s, color 0.2s, box-shadow 0.2s;
      margin-right: 10px;
      margin-bottom: 0;
      outline: none;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }
    .btn-primary {
      background: #1a73e8;
      color: #fff;
    }
    .btn-primary:hover {
      background: #1764cf;
    }
    .btn-success {
      background: #34a853;
      color: #fff;
    }
    .btn-success:hover {
      background: #2c8c4a;
    }
    .btn-secondary {
      background: #f0f0f0;
      color: #333;
      border: 1px solid #d0d0d0;
    }
    .btn-secondary:hover {
      background: #e0e0e0;
    }

    /* Loader and Message Box */
    .loader-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      visibility: hidden;
      opacity: 0;
      transition: visibility 0s, opacity 0.3s linear;
    }

    .loader-overlay.show {
      visibility: visible;
      opacity: 1;
    }

    .spinner {
      border: 8px solid #f3f3f3;
      border-top: 8px solid #1a73e8;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .message-box {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: #ffffff;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
      z-index: 1001;
      text-align: center;
      min-width: 300px;
      max-width: 90%;
      visibility: hidden;
      opacity: 0;
      transition: visibility 0s, opacity 0.3s linear;
    }

    .message-box.show {
      visibility: visible;
      opacity: 1;
    }

    .message-box p {
      font-size: 1.1rem;
      margin-bottom: 20px;
      color: #333;
    }

    .message-box button {
      background-color: #1a73e8;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 1rem;
      transition: background-color 0.3s ease;
    }

    .message-box button:hover {
      background-color: #1764cf;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .form-grid-layout {
        grid-template-columns: 1fr; /* Single column on smaller screens */
      }
      .form-container {
        padding: 20px;
      }
      .form-actions {
        flex-direction: column;
        gap: 10px;
      }
      .btn-cancel, .btn-save-continue, .btn-save-draft {
        width: 100%;
      }
      .file-input-wrapper {
        flex-direction: column;
        align-items: flex-start;
      }
      .custom-file-upload-button {
        width: 100%;
        margin-bottom: 10px;
      }
      .file-name-display {
        margin-left: 0;
        text-align: center;
      }
    }
  </style>
</head>
<body>
  <div class="loader-overlay" id="loader">
    <div class="spinner"></div>
  </div>

  <div class="message-box" id="messageBox">
    <p id="messageText"></p>
    <button id="messageBoxClose">OK</button>
  </div>

  <div class="form-container">
    <!-- Header -->
    <div class="header">
      <h1>CDR Application Form</h1>
    </div>
    <form id="cdrApplicationForm">
      <!-- Hidden field for rowIndex for editing -->
      <input type="hidden" id="rowIndex" name="rowIndex">

      <!-- Section: Application Details -->
      <div class="section-card">
        <h3>Application Details</h3>
        <div class="form-grid-layout">
          <div class="form-group">
            <label for="logBookNo">Log Book No <span style="color:red;">*</span></label>
            <input type="text" id="logBookNo" name="logBookNo" required>
            <span class="error-message" id="logBookNoError">This field is required.</span>
          </div>

          <div class="form-group">
            <label for="policeStation">Police Station/ Office <span style="color:red;">*</span></label>
            <select id="policeStation" name="policeStation" required>
              <option value="">Select Police Station</option>
            </select>
            <span class="error-message" id="policeStationError">This field is required.</span>
          </div>

          <div class="form-group checkbox-group">
            <label>Request Type <span style="color:red;">*</span></label>
            <div class="checkbox-options">
              <div class="checkbox-option">
                <input type="checkbox" id="requestTypeCDR" name="requestType" value="Call Data Records">
                <label for="requestTypeCDR">Call Data Records</label>
              </div>
              <div class="checkbox-option">
                <input type="checkbox" id="requestTypeCertifiedCDR" name="requestType" value="Certified Call Data Records">
                <label for="requestTypeCertifiedCDR">Certified Call Data Records</label>
              </div>
              <div class="checkbox-option">
                <input type="checkbox" id="requestTypeCAF" name="requestType" value="Customer Application Form">
                <label for="requestTypeCAF">Customer Application Form</label>
              </div>
              <div class="checkbox-option">
                <input type="checkbox" id="requestTypeCertifiedCAF" name="requestType" value="Certified Customer Application Form">
                <label for="requestTypeCertifiedCAF">Certified Customer Application Form</label>
              </div>
              <div class="checkbox-option">
                <input type="checkbox" id="requestTypeLocation" name="requestType" value="Location">
                <label for="requestTypeLocation">Location</label>
              </div>
            </div>
            <span class="error-message" id="requestTypeError">At least one request type is required.</span>
          </div>
        </div>
      </div>

      <!-- Section: Complainant Details -->
      <div class="section-card">
        <h3>Complainant Details</h3>
        <div class="form-grid-layout">
          <div class="form-group">
            <label for="officerName">Name of Complainant (Officer) <span style="color:red;">*</span></label>
            <input type="text" id="officerName" name="officerName" required>
            <span class="error-message" id="officerNameError">This field is required.</span>
          </div>

          <div class="form-group">
            <label for="designation">Designation <span style="color:red;">*</span></label>
            <select id="designation" name="designation" required>
              <option value="">Select Designation</option>
            </select>
            <span class="error-message" id="designationError">This field is required.</span>
          </div>
        </div>
      </div>

      <!-- Section: Crime Details -->
      <div class="section-card">
        <h3>Crime Details</h3>
        <div class="form-grid-layout">
          <div class="form-group">
            <label for="crimeType">Crime Type <span style="color:red;">*</span></label>
            <select id="crimeType" name="crimeType" required>
              <option value="">Select Crime Type</option>
            </select>
            <span class="error-message" id="crimeTypeError">This field is required.</span>
          </div>

          <div class="form-group">
            <label for="crimeNo">Crime No (XXX/YYYY format) <span style="color:red;">*</span></label>
            <input type="text" id="crimeNo" name="crimeNo" pattern="^\d{2,4}\/\d{4}$" required placeholder="e.g., 123/2024">
            <span class="error-message" id="crimeNoError">Format must be XXX/YYYY (e.g., 123/2024).</span>
          </div>

          <div class="form-group">
            <label for="officeWhereCrimeIsRegistered">Office Where Crime is Registered <span style="color:red;">*</span></label>
            <select id="officeWhereCrimeIsRegistered" name="officeWhereCrimeIsRegistered" required>
              <option value="">Select Office</option>
            </select>
            <span class="error-message" id="officeWhereCrimeIsRegisteredError">This field is required.</span>
          </div>

          <div class="form-group">
            <label for="underSection">Under Section <span style="color:red;">*</span></label>
            <input type="text" id="underSection" name="underSection" required>
            <span class="error-message" id="underSectionError">This field is required.</span>
          </div>

          <div class="form-group">
            <label for="dateOfReport">Date of Report <span style="color:red;">*</span></label>
            <input type="date" id="dateOfReport" name="dateOfReport" required>
            <span class="error-message" id="dateOfReportError">This field is required. Date cannot be in the future.</span>
          </div>

          <div class="form-group">
            <label for="dateOfOccurrence">Date of Occurrence <span style="color:red;">*</span></label>
            <input type="date" id="dateOfOccurrence" name="dateOfOccurrence" required>
            <span class="error-message" id="dateOfOccurrenceError">This field is required. Date cannot be in the future.</span>
          </div>

          <div class="form-group full-width"> <!-- Added full-width class -->
            <label for="placeOfOccurrence">Place of Occurrence <span style="color:red;">*</span></label>
            <input type="text" id="placeOfOccurrence" name="placeOfOccurrence" required>
            <span class="error-message" id="placeOfOccurrenceError">This field is required.</span>
          </div>

          <div class="form-group full-width"> <!-- Added full-width class -->
            <label for="gistOfTheCase">Gist of the case <span style="color:red;">*</span></label>
            <textarea id="gistOfTheCase" name="gistOfTheCase" required></textarea>
            <span class="error-message" id="gistOfTheCaseError">This field is required.</span>
          </div>
        </div>
      </div>

      <!-- Section: Accused & Phone/IMEI Details -->
      <div class="section-card">
        <h3>Accused & Phone/IMEI Details</h3>
        <div class="form-grid-layout">
          <div class="form-group dynamic-field-group">
            <label>Name of Accused <span style="color:red;">*</span></label>
            <div id="accusedFields" class="dynamic-field-container">
              <!-- Dynamic accused name fields will be added here -->
            </div>
            <span class="error-message" id="nameOfAccusedError">At least one accused name is required.</span>
          </div>

          <div class="form-group dynamic-field-group">
            <label>Phone No/ IMEI No <span style="color:red;">*</span></label>
            <div id="phoneImeiFields" class="dynamic-field-container">
              <!-- Dynamic phone number fields will be added here -->
            </div>
            <span class="error-message" id="phoneImeiNoError">At least one phone/IMEI number is required.</span>
          </div>
        </div>
      </div>

      <!-- Section: Period & Purpose -->
      <div class="section-card">
        <h3>Period & Purpose</h3>
        <div class="form-grid-layout">
          <div class="form-group">
            <label for="fromDate">Period From <span style="color:red;">*</span></label>
            <input type="date" id="fromDate" name="fromDate" required>
            <span class="error-message" id="fromDateError">This field is required. Must be within the last 2 years and not a future date.</span>
          </div>

          <div class="form-group">
            <label for="toDate">Period To <span style="color:red;">*</span></label>
            <input type="date" id="toDate" name="toDate" required>
            <span class="error-message" id="toDateError">This field is required. Cannot be a future date and cannot be older than 'Period From'.</span>
          </div>

          <div class="form-group full-width"> <!-- Added full-width class -->
            <label for="purposeOfDetailsSought">Purpose of details sought <span style="color:red;">*</span></label>
            <textarea id="purposeOfDetailsSought" name="purposeOfDetailsSought" required></textarea>
            <span class="error-message" id="purposeOfDetailsSoughtError">This field is required.</span>
          </div>
        </div>
      </div>

      <!-- Section: Submission & Images -->
      <div class="section-card">
        <h3>Submission Details & Images</h3>
        <div class="form-grid-layout">
          <div class="form-group">
            <label for="submissionDate">Submission Date</label>
            <input type="date" id="submissionDate" name="submissionDate" readonly required />
          </div>

          <div class="form-group">
            <label for="submissionPlace">Submission Place <span style="color:red;">*</span></label>
            <input type="text" id="submissionPlace" name="submissionPlace" required>
            <span class="error-message" id="submissionPlaceError">This field is required.</span>
          </div>

          <div class="form-group file-upload-group">
            <label for="officerSignature">Officer Signature (PNG/JPG) <span style="color:red;">*</span></label>
            <div class="file-input-wrapper">
              <label for="officerSignature" class="custom-file-upload-button">Choose File</label>
              <input type="file" id="officerSignature" name="officerSignature" accept="image/*">
              <span class="file-name-display" id="officerSignatureFileName">No file chosen</span>
            </div>
            <div class="image-preview" id="officerSignaturePreview">
              <span>Signature Preview</span>
              <img src="" alt="Officer Signature Preview" style="display:none;">
            </div>
            <span class="error-message" id="officerSignatureError">Officer signature is required.</span>
          </div>

          <div class="form-group file-upload-group">
            <label for="officeSeal">Office Seal (PNG/JPG) <span style="color:red;">*</span></label>
            <div class="file-input-wrapper">
              <label for="officeSeal" class="custom-file-upload-button">Choose File</label>
              <input type="file" id="officeSeal" name="officeSeal" accept="image/*">
              <span class="file-name-display" id="officeSealFileName">No file chosen</span>
            </div>
            <div class="image-preview" id="officeSealPreview">
              <span>Seal Preview</span>
              <img src="" alt="Office Seal Preview" style="display:none;">
            </div>
            <span class="error-message" id="officeSealError">Office seal is required.</span>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-secondary" onclick="handleCancel()">Cancel</button>
        <button type="button" class="btn btn-primary" onclick="handleSaveDraft()">Save Draft</button>
        <button type="button" class="btn btn-success" onclick="handleSaveAndContinue()">Save & Continue</button>
      </div>
    </form>
  </div>


  <script>
    // Custom Message Box and Loader functions (re-defined for this HTML file)
    const loader = document.getElementById('loader');
    const messageBox = document.getElementById('messageBox');
    const messageText = document.getElementById('messageText');
    const messageBoxClose = document.getElementById('messageBoxClose');

    function showLoader() {
      loader.classList.add('show');
      console.log('Loader shown.');
    }

    function hideLoader() {
      loader.classList.remove('show');
      console.log('Loader hidden.');
    }

    function showMessageBox(message, callback) {
      messageText.textContent = message;
      messageBox.classList.add('show');
      console.log('Message box shown: ' + message);
      messageBoxClose.onclick = () => {
        hideMessageBox();
        if (callback) callback();
      };
    }

    function hideMessageBox() {
      messageBox.classList.remove('show');
      console.log('Message box hidden.');
    }

    // Global variables for current editing context
    let currentEditRowIndex = sessionStorage.getItem('editRowIndex');
    let officerSignatureBase64 = sessionStorage.getItem('officerSignatureBase64') || null;
    let officeSealBase64 = sessionStorage.getItem('officeSealBase64') || null;
    let officerSignatureID = sessionStorage.getItem('officerSignatureID') || null;
    let officeSealID = sessionStorage.getItem('officeSealID') || null;


    /**
     * Reads a file as a Base64 encoded string.
     * @param {File} file - The file to read.
     * @returns {Promise<string>} A promise that resolves with the Base64 string.
     */
    function readFileAsBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result.split(',')[1]); // Get only the base64 part
        reader.onerror = error => reject(error);
        reader.readAsDataURL(file);
      });
    }

    /**
     * Populates a select dropdown with options.
     * @param {string} selectId - The ID of the select element.
     * @param {Array<string>} options - An array of strings for the options.
     */
    function populateDropdown(selectId, options) {
      const selectElement = document.getElementById(selectId);
      // Clear previous options except the first "Select..." option if it exists
      const defaultOption = selectElement.querySelector('option[value=""]');
      selectElement.innerHTML = '';
      if (defaultOption) {
        selectElement.appendChild(defaultOption);
      } else {
        const placeholderOption = document.createElement('option');
        placeholderOption.value = "";
        placeholderOption.textContent = `Select ${selectId.replace(/([A-Z])/g, ' $1').trim()}`; // e.g., "Select Police Station"
        selectElement.appendChild(placeholderOption);
      }

      options.forEach(option => {
        const opt = document.createElement('option');
        opt.value = option;
        opt.textContent = option;
        selectElement.appendChild(opt);
      });
      console.log(`Dropdown '${selectId}' populated with ${options.length} options.`);
    }

    /**
     * Adds a new input field for dynamic lists (Accused, Phone/IMEI).
     * @param {string} containerId - The ID of the container div.
     * @param {string} inputClass - The class for the input field.
     * @param {string} placeholder - Placeholder text for the input.
     * @param {string} [value=''] - Initial value for the input.
     */
    function addDynamicField(containerId, inputClass, placeholder, value = '') {
      const container = document.getElementById(containerId);
      const wrapper = document.createElement('div');
      wrapper.className = 'dynamic-input-wrapper';

      const input = document.createElement('input');
      input.type = 'text';
      input.name = (inputClass === 'accused-input' ? 'nameOfAccused' : 'phoneNoIMEINo');
      input.className = inputClass;
      input.required = true;
      input.placeholder = placeholder;
      input.value = value; // Set initial value
      input.addEventListener('blur', () => validateField(input)); // Add blur listener for new fields
      wrapper.appendChild(input);

      const addButton = document.createElement('button');
      addButton.type = 'button';
      addButton.className = 'btn-add';
      addButton.textContent = '+';
      addButton.onclick = () => addDynamicField(containerId, inputClass, placeholder);
      wrapper.appendChild(addButton);

      const removeButton = document.createElement('button');
      removeButton.type = 'button';
      removeButton.className = 'btn-remove';
      removeButton.textContent = '-';
      removeButton.onclick = () => removeDynamicField(wrapper, containerId, inputClass);
      wrapper.appendChild(removeButton);

      container.appendChild(wrapper);

      // Trigger re-validation on addition
      validateField(input);
      updateDynamicFieldErrors(containerId, inputClass);
      console.log(`Added new dynamic field to ${containerId}`);
    }

    /**
     * Removes an input field from dynamic lists.
     * @param {HTMLElement} wrapper - The wrapper div containing the input and remove button.
     * @param {string} containerId - The ID of the container div.
     * @param {string} inputClass - The class for the input field.
     */
    function removeDynamicField(wrapper, containerId, inputClass) {
      const container = document.getElementById(containerId);
      const currentInputs = container.querySelectorAll(`.${inputClass}`);
      if (currentInputs.length > 1) {
        container.removeChild(wrapper);
        console.log(`Removed dynamic field from ${containerId}`);
      } else {
        // If it's the last field, clear its value instead of removing it,
        // and ensure the error message is displayed if it's now empty.
        const lastInput = currentInputs[0];
        if (lastInput) {
            lastInput.value = '';
            validateField(lastInput); // Revalidate to show required error
        }
        showMessageBox('At least one field is required.');
        console.warn('Attempted to remove last dynamic field in ' + containerId + '. Cleared value instead.');
      }
      // Trigger re-validation on removal
      updateDynamicFieldErrors(containerId, inputClass);
    }

    /**
     * Updates error messages for dynamic fields.
     * @param {string} containerId
     * @param {string} inputClass
     */
    function updateDynamicFieldErrors(containerId, inputClass) {
      const inputs = document.querySelectorAll(`#${containerId} .${inputClass}`);
      const errorMessageId = inputClass.includes('accused') ? 'nameOfAccusedError' : 'phoneImeiNoError';
      const errorMessageElement = document.getElementById(errorMessageId);
      const hasValidInput = Array.from(inputs).some(input => input.value.trim() !== '');

      if (!hasValidInput) {
        errorMessageElement.style.display = 'block';
        console.log(`Error shown for ${errorMessageId}: No valid input.`);
      } else {
        errorMessageElement.style.display = 'none';
        console.log(`Error hidden for ${errorMessageId}: Valid input found.`);
      }
    }


    /**
     * Handles the complex validation logic for requestType checkboxes.
     */
    function handleRequestTypeLogic() {
      console.log('Running request type logic...');
      const cdr = document.getElementById('requestTypeCDR');
      const certifiedCdr = document.getElementById('requestTypeCertifiedCDR');
      const caf = document.getElementById('requestTypeCAF');
      const certifiedCaf = document.getElementById('requestTypeCertifiedCAF');
      const location = document.getElementById('requestTypeLocation');

      const allCheckboxes = [cdr, certifiedCdr, caf, certifiedCaf, location];

      // Re-enable all checkboxes before applying rules to ensure correct state from scratch
      allCheckboxes.forEach(cb => {
        cb.disabled = false;
      });

      if (location.checked) {
        console.log('Location is checked. Disabling others.');
        allCheckboxes.forEach(cb => {
          if (cb !== location) {
            cb.checked = false;
            cb.disabled = true;
          }
        });
      } else {
        // Rule: CDR, Certified CDR, Certified CAF cannot be selected together.
        // If CDR is checked: disable Certified CDR and Certified CAF. CAF is enabled.
        if (cdr.checked) {
          certifiedCdr.checked = false; certifiedCdr.disabled = true;
          certifiedCaf.checked = false; certifiedCaf.disabled = true;
        }
        // If Certified CDR is checked: disable CDR and CAF. Certified CAF is enabled.
        if (certifiedCdr.checked) {
          cdr.checked = false; cdr.disabled = true;
          caf.checked = false; caf.disabled = true;
        }
        // If CAF is checked: disable Certified CAF, Certified CDR. CDR is enabled.
        if (caf.checked) {
          certifiedCaf.checked = false; certifiedCaf.disabled = true;
          certifiedCdr.checked = false; certifiedCdr.disabled = true;
        }
        // If Certified CAF is checked: disable CAF, CDR. Certified CDR is enabled.
        if (certifiedCaf.checked) {
          caf.checked = false; caf.disabled = true;
          cdr.checked = false; cdr.disabled = true;
        }
      }
      console.log('Finished request type logic.');
      validateRequestType(); // Re-validate checkbox group after logic
    }

    /**
     * Validates the request type checkbox group.
     * @returns {boolean} True if at least one checkbox is selected, false otherwise.
     */
    function validateRequestType() {
        const checkboxes = document.querySelectorAll('input[name="requestType"]');
        const anyCheckboxChecked = Array.from(checkboxes).some(cb => cb.checked);
        const requestTypeError = document.getElementById('requestTypeError');

        if (!anyCheckboxChecked) {
            requestTypeError.style.display = 'block';
            console.log('Request Type validation failed: None selected.');
            return false;
        } else {
            requestTypeError.style.display = 'none';
            return true;
        }
    }


    /**
     * Validates a single form field and shows/hides error messages.
     * @param {HTMLElement} inputElement - The input, select, or textarea element.
     * @returns {boolean} True if valid, false otherwise.
     */
    function validateField(inputElement) {
      // Exclude hidden inputs and file/checkbox/dynamic fields from this general validation
      if (inputElement.type === 'hidden' || inputElement.type === 'file' || inputElement.type === 'checkbox' ||
          inputElement.name === 'nameOfAccused' || inputElement.name === 'phoneNoIMEINo') {
        return true; // Handled separately or not applicable for general validation
      }

      const errorSpan = document.getElementById(inputElement.id + 'Error');
      let isValid = true;
      let customErrorMessage = ""; // To store specific custom error messages

      // Standard HTML5 validity check
      if (!inputElement.checkValidity()) {
        isValid = false;
        customErrorMessage = inputElement.validationMessage || "This field is required.";
      } else {
        // Custom validation for crimeNo format
        if (inputElement.id === 'crimeNo') {
          const pattern = /^\d{2,4}\/\d{4}$/;
          if (!pattern.test(inputElement.value)) {
            isValid = false;
            customErrorMessage = "Format must be XXX/YYYY (e.g., 123/2024).";
          }
        }
        // Custom validation for native HTML5 date inputs
        else if (inputElement.type === 'date') {
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          const inputValue = inputElement.value;

          if (!inputValue) {
              isValid = false;
              customErrorMessage = "This field is required.";
          } else {
              const inputDate = new Date(inputValue);
              inputDate.setHours(0, 0, 0, 0);

              if (['dateOfReport', 'dateOfOccurrence', 'submissionDate'].includes(inputElement.id)) {
                  if (inputDate > today) {
                      isValid = false;
                      customErrorMessage = "Date cannot be in the future.";
                  }
              }

              if (inputElement.id === 'fromDate') {
                const twoYearsAgo = new Date();
                twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
                twoYearsAgo.setHours(0, 0, 0, 0);

                if (inputDate < twoYearsAgo) {
                  isValid = false;
                  customErrorMessage = "Period From must be within the last 2 years.";
                }
              } else if (inputElement.id === 'toDate') {
                const fromDateElement = document.getElementById('fromDate');
                const fromDateValue = fromDateElement.value;

                if (fromDateValue) {
                  const fromDate = new Date(fromDateValue);
                  fromDate.setHours(0, 0, 0, 0);

                  if (inputDate < fromDate) {
                    isValid = false;
                    customErrorMessage = "Period To cannot be older than 'Period From'.";
                  }
                }
              }
          }
        }
      }

      // Update error span visibility and text if it exists
      if (errorSpan) {
        if (!isValid) {
          errorSpan.textContent = customErrorMessage;
          errorSpan.style.display = 'block';
          console.log(`Validation failed for ${inputElement.id}: ${customErrorMessage}`);
        } else {
          errorSpan.style.display = 'none';
          console.log(`Validation passed for ${inputElement.id}.`);
        }
      } else {
        // This else block is for inputs that don't have a specific error span but are being validated.
        // Log a warning if a required field doesn't have an error span, but don't break.
        if (!isValid) {
          console.warn(`Validation failed for ${inputElement.id}, but no error span found with ID ${inputElement.id + 'Error'}. Message: ${customErrorMessage}`);
        }
      }

      return isValid;
    }

    /**
     * Performs all client-side validations.
     * @returns {boolean} True if all validations pass, false otherwise.
     */
    function validateForm() {
      console.log('Starting full form validation...');
      let formIsValid = true;
      const form = document.getElementById('cdrApplicationForm');
      // Query all relevant form inputs, excluding hidden, file, and checkbox inputs for general validation.
      const allInputs = form.querySelectorAll('input:not([type="hidden"]):not([type="file"]):not([type="checkbox"])[required], select[required], textarea[required]');

      allInputs.forEach(input => {
        if (!validateField(input)) {
          formIsValid = false;
        }
      });

      // Specific validation for dynamic fields to ensure at least one has a value
      updateDynamicFieldErrors('accusedFields', 'accused-input');
      updateDynamicFieldErrors('phoneImeiFields', 'phone-imei-input');
      if (document.getElementById('nameOfAccusedError').style.display === 'block' ||
          document.getElementById('phoneImeiNoError').style.display === 'block') {
        formIsValid = false;
        console.log('Dynamic fields validation failed.');
      }

      // Checkbox group validation
      if (!validateRequestType()) {
        formIsValid = false;
        console.log('Request Type checkboxes validation failed.');
      }

      // File upload validation: Only require if new files are not already uploaded AND no existing IDs
      const officerSignatureInput = document.getElementById('officerSignature');
      const officerSignatureError = document.getElementById('officerSignatureError');
      if (!officerSignatureID && officerSignatureInput.files.length === 0) {
        officerSignatureError.style.display = 'block';
        formIsValid = false;
        console.log('Officer signature required.');
      } else {
        officerSignatureError.style.display = 'none';
      }

      const officeSealInput = document.getElementById('officeSeal');
      const officeSealError = document.getElementById('officeSealError');
      if (!officeSealID && officeSealInput.files.length === 0) {
        officeSealError.style.display = 'block';
        formIsValid = false;
        console.log('Office seal required.');
      } else {
        officeSealError.style.display = 'none';
      }

      console.log('Form validation complete. Is form valid? ' + formIsValid);
      return formIsValid;
    }


    /**
     * Handles the form submission (Save & Continue or Save Draft).
     * @param {Event} event - The event object.
     * @param {string} actionType - 'Draft' or 'DraftAndPreview'.
     */
    async function handleSubmit(event, actionType) {
      event.preventDefault(); // Prevent default form submission
      console.log(`Form submission initiated with actionType: ${actionType}`);
      showLoader(); // Show loader immediately on button click

      // Perform validation only if the action is 'DraftAndPreview'
      let isFormFullyValid = true;
      if (actionType === 'DraftAndPreview') {
          isFormFullyValid = validateForm();
          if (!isFormFullyValid) {
              hideLoader();
              showMessageBox('Please correct the highlighted errors before continuing.');
              console.log('Form validation failed for "Save & Continue", stopping submission.');
              return;
          }
      } else if (actionType === 'Draft') {
          // For 'Save Draft', we still run validation to show errors to the user,
          // but we don't block the submission if there are errors (as per requirements).
          validateForm();
      }

      const form = document.getElementById('cdrApplicationForm');
      const formData = {};

      // Collect all input values by name
      form.querySelectorAll('input, select, textarea').forEach(input => {
        if (input.name) {
          if (input.type === 'checkbox') {
            if (!formData[input.name]) {
              formData[input.name] = [];
            }
            if (input.checked) {
              formData[input.name].push(input.value);
            }
          } else if (input.name === 'nameOfAccused' || input.name === 'phoneNoIMEINo') {
            if (!formData[input.name]) {
              formData[input.name] = [];
            }
            // Only add if not empty
            if (input.value.trim() !== '') {
              formData[input.name].push(input.value.trim());
            }
          } else if (input.type !== 'file') { // Exclude file inputs as they are handled separately for base64
            formData[input.name] = input.value;
          }
        }
      });
      console.log('Collected form data before image processing:', formData);

      // Handle image file processing
      const officerSignatureInput = document.getElementById('officerSignature');
      const officeSealInput = document.getElementById('officeSeal');

      try {
        // Process Officer Signature
        if (officerSignatureInput.files.length > 0) {
          officerSignatureBase64 = await readFileAsBase64(officerSignatureInput.files[0]);
          formData.officerSignatureBase64 = officerSignatureBase64;
          formData.officerSignatureID = null; // Clear existing ID if new file is uploaded
          console.log('New officer signature base64 collected.');
        } else if (officerSignatureID) {
           formData.officerSignatureID = officerSignatureID; // Keep existing ID
           formData.officerSignatureBase64 = null; // No new base64 to upload
        } else {
           delete formData.officerSignatureID;
           delete formData.officerSignatureBase64;
           console.log('No officer signature to process.');
        }

        // Process Office Seal
        if (officeSealInput.files.length > 0) {
          officeSealBase64 = await readFileAsBase64(officeSealInput.files[0]);
          formData.officeSealBase64 = officeSealBase64;
          formData.officeSealID = null; // Clear existing ID if new file is uploaded
          console.log('New office seal base64 collected.');
        } else if (officeSealID) {
           formData.officeSealID = officeSealID; // Keep existing ID
           formData.officeSealBase64 = null; // No new base64 to upload
        } else {
           delete formData.officeSealID;
           delete formData.officeSealBase64;
           console.log('No office seal to process.');
        }

      } catch (error) {
        hideLoader();
        showMessageBox('Error reading image files: ' + error.message);
        console.error('File read error:', error);
        return;
      }

      console.log('Calling saveApplication on server-side with final formData:', formData);
      // Call Apps Script function to save data
      google.script.run
        .withSuccessHandler(function(response) {
          hideLoader();
          console.log('saveApplication success response:', response);
          if (response.success) {
            // Update sessionStorage with the latest data and IDs returned from server
            sessionStorage.setItem('applicationData', JSON.stringify(response.formData));
            sessionStorage.setItem('editRowIndex', response.rowIndex); // Update rowIndex in case it was a new row
            sessionStorage.setItem('officerSignatureID', response.formData.officerSignatureID || '');
            sessionStorage.setItem('officeSealID', response.formData.officeSealID || '');
            currentEditRowIndex = response.rowIndex; // Update client-side global

            if (actionType === 'DraftAndPreview') {
                console.log('Navigating to preview...');
                // We show the preview on top, but the InputForm stays open underneath.
                // The prompt for InputForm "On success, calls google.script.run.showPreview() to open Preview.html."
                // The prompt does NOT say to close InputForm here, which implies it should stay open.
                google.script.run
                    .withSuccessHandler(function() {
                        console.log('Preview form opened successfully.');
                    })
                    .withFailureHandler(function(error) {
                        console.error("showPreview failure:", error);
                        onFailure(error);
                    })
                    .showPreview(); // Open Preview
            } else if (actionType === 'Draft') { // For 'Save Draft' button
                showMessageBox(response.message, () => {
                    // After saving draft, close the form and return to dashboard
                    sessionStorage.removeItem('editRowIndex'); // Clear edit context as we're leaving the form
                    sessionStorage.removeItem('applicationData');
                    sessionStorage.removeItem('officerSignatureBase64');
                    sessionStorage.removeItem('officeSealBase64');
                    sessionStorage.removeItem('officerSignatureID');
                    sessionStorage.removeItem('officeSealID');

                    // Close current form - dashboard should be visible underneath
                    google.script.host.close();
                    console.log('InputForm closed, returning to Dashboard.');
                });
            }
          } else {
            showMessageBox('Failed to save application: ' + response.message);
          }
        })
        .withFailureHandler(function(error) {
            console.error("saveApplication failure:", error);
            onFailure(error);
        })
        .saveApplication(formData, 'Draft', currentEditRowIndex); // Always save as 'Draft' from this form
    }

    // Centralized onFailure handler
    function onFailure(error) {
        hideLoader();
        console.error("Error from Apps Script:", error);
        // Ensure error.message is accessible; sometimes it might be just a string.
        showMessageBox('An error occurred: ' + (error.message || error));
    }

    /**
     * Handles the cancel button click. Closes the current form and asks Code.gs to show Dashboard.
     */
    function handleCancel() {
      console.log('Cancel button clicked. Clearing session storage and closing form.');
      sessionStorage.removeItem('editRowIndex');
      sessionStorage.removeItem('applicationData');
      sessionStorage.removeItem('officerSignatureBase64');
      sessionStorage.removeItem('officeSealBase64');
      sessionStorage.removeItem('officerSignatureID');
      sessionStorage.removeItem('officeSealID');

      showLoader(); // Show loader while transitioning back to dashboard
      // The server-side function just returns success. The actual close happens here.
      google.script.host.close(); // Close this InputForm modal
      console.log('InputForm closed by cancel. Dashboard should be visible or reloaded.');
    }

    /**
     * Initializes the form on page load.
     */
    window.onload = function() {
      console.log('InputForm.html loaded. Initializing...');

      // Initialize native HTML5 date inputs
      const today = new Date().toISOString().split('T')[0];
      const twoYearsAgo = new Date();
      twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
      const twoYearsAgoStr = twoYearsAgo.toISOString().split('T')[0];

      // Set max date for all date inputs to today (cannot be future)
      document.getElementById('dateOfReport').max = today;
      document.getElementById('dateOfOccurrence').max = today;
      document.getElementById('fromDate').max = today;
      document.getElementById('fromDate').min = twoYearsAgoStr; // Min date is 2 years ago
      document.getElementById('toDate').max = today;
      document.getElementById('submissionDate').max = today;
      document.getElementById('submissionDate').value = today; // Default to today
      document.getElementById('submissionDate').readOnly = true; // Make submission date read-only

      // Add event listener for fromDate to update toDate's min value
      document.getElementById('fromDate').addEventListener('change', function() {
        const fromDateValue = this.value;
        const toDateInput = document.getElementById('toDate');
        if (fromDateValue) {
          toDateInput.min = fromDateValue;
          // If toDate is currently older than new fromDate, clear it
          if (toDateInput.value && toDateInput.value < fromDateValue) {
            toDateInput.value = '';
          }
        }
        validateField(this);
        validateField(toDateInput);
      });

      // Add change event listeners for date validation
      document.getElementById('dateOfReport').addEventListener('change', function() {
        validateField(this);
      });
      document.getElementById('dateOfOccurrence').addEventListener('change', function() {
        validateField(this);
      });
      document.getElementById('toDate').addEventListener('change', function() {
        validateField(this);
      });
      document.getElementById('submissionDate').addEventListener('change', function() {
        validateField(this);
      });

      console.log('Native HTML5 date inputs initialized.');


      // Populate dropdowns
      showLoader();
      console.log('Fetching dropdown options from server...');
      google.script.run
        .withSuccessHandler(function(options) {
          console.log('Dropdown options received:', options);
          populateDropdown('policeStation', options.policeStations);
          populateDropdown('crimeType', options.crimeTypes);
          populateDropdown('designation', options.designations);
          populateDropdown('officeWhereCrimeIsRegistered', options.officeWhereCrimeIsRegisteredOptions);
          hideLoader();

          // Load existing data if editing
          if (currentEditRowIndex) {
            console.log('Edit mode detected. Loading application data for row index:', currentEditRowIndex);
            showLoader(); // Keep loader visible while loading existing data
            google.script.run
              .withSuccessHandler(function(response) {
                hideLoader();
                console.log('Application data received for editing:', response);
                if (response.success && response.appData) {
                  const appData = response.appData;
                  document.getElementById('logBookNo').value = appData.logBookNo || '';
                  document.getElementById('policeStation').value = appData.policeStation || '';
                  document.getElementById('crimeType').value = appData.crimeType || '';
                  document.getElementById('crimeNo').value = appData.crimeNo || '';
                  document.getElementById('officeWhereCrimeIsRegistered').value = appData.officeWhereCrimeIsRegistered || '';
                  document.getElementById('underSection').value = appData.underSection || '';

                  // Set native date input values
                  if (appData.dateOfReport) document.getElementById('dateOfReport').value = appData.dateOfReport;
                  if (appData.dateOfOccurrence) document.getElementById('dateOfOccurrence').value = appData.dateOfOccurrence;

                  document.getElementById('placeOfOccurrence').value = appData.placeOfOccurrence || '';
                  document.getElementById('officerName').value = appData.officerName || '';
                  document.getElementById('designation').value = appData.designation || '';
                  document.getElementById('gistOfTheCase').value = appData.gistOfTheCase || '';

                  // Populate dynamic fields
                  populateDynamicFields('accusedFields', 'accused-input', 'Name of Accused', appData.nameOfAccused);
                  populateDynamicFields('phoneImeiFields', 'phone-imei-input', 'Phone No/ IMEI No', appData.phoneNoIMEINo);
                  console.log('Dynamic fields populated.');

                  // Populate requestType checkboxes
                  document.querySelectorAll('input[name="requestType"]').forEach(cb => cb.checked = false); // Clear first
                  if (appData.requestType) {
                    appData.requestType.forEach(type => {
                      const checkbox = document.querySelector(`input[name="requestType"][value="${type}"]`);
                      if (checkbox) checkbox.checked = true;
                    });
                    console.log('Request type checkboxes populated.');
                  }
                  handleRequestTypeLogic(); // Apply logic after populating
                  console.log('Request type logic applied after data load.');

                  if (appData.fromDate) document.getElementById('fromDate').value = appData.fromDate;
                  if (appData.toDate) document.getElementById('toDate').value = appData.toDate;

                  document.getElementById('purposeOfDetailsSought').value = appData.purposeOfDetailsSought || '';
                  if (appData.submissionDate) document.getElementById('submissionDate').value = appData.submissionDate;
                  document.getElementById('submissionPlace').value = appData.submissionPlace || '';

                  // Store image IDs for re-use if no new files are uploaded
                  officerSignatureID = appData.officerSignatureID || null;
                  officeSealID = appData.officeSealID || null;
                  console.log('Image IDs stored:', {officerSignatureID, officeSealID});

                  // Display existing image previews if IDs are available
                  if (officerSignatureID) {
                      // Using the Google Drive direct image link format
                      document.getElementById('officerSignaturePreview').innerHTML = `<img src="https://lh3.googleusercontent.com/d/${officerSignatureID}" alt="Officer Signature" style="max-width:100%;max-height:100%;object-fit:contain;">`;
                      document.getElementById('officerSignatureFileName').textContent = 'Existing file loaded';
                      console.log('Officer signature preview loaded from ID.');
                  }
                  if (officeSealID) {
                      document.getElementById('officeSealPreview').innerHTML = `<img src="https://lh3.googleusercontent.com/d/${officeSealID}" alt="Office Seal" style="max-width:100%;max-height:100%;object-fit:contain;">`;
                      document.getElementById('officeSealFileName').textContent = 'Existing file loaded';
                      console.log('Office seal preview loaded from ID.');
                  }

                } else {
                  showMessageBox('Failed to load application data: ' + response.message);
                  console.error('Failed to load application data:', response.message);
                }
                hideLoader(); // Hide loader after data load attempt
              })
              .withFailureHandler(onFailure) // Use the centralized onFailure
              .loadApplication(parseInt(currentEditRowIndex));
          } else {
            hideLoader(); // Hide loader if not in edit mode (no data to load)
            // Ensure initial dynamic fields are present for new applications
            document.getElementById('accusedFields').innerHTML = ''; // Ensure clear
            document.getElementById('phoneImeiFields').innerHTML = ''; // Ensure clear
            addDynamicField('accusedFields', 'accused-input', 'Name of Accused', '');
            addDynamicField('phoneImeiFields', 'phone-imei-input', 'Phone No/ IMEI No', '');
            console.log('Not in edit mode. Initial dynamic fields set.');
          }
        })
        .withFailureHandler(onFailure) // Use the centralized onFailure
        .getDropdownOptions();


      // Event listeners for requestType checkboxes
      document.querySelectorAll('input[name="requestType"]').forEach(checkbox => {
        checkbox.addEventListener('change', handleRequestTypeLogic);
      });
      console.log('Request type change listeners set.');

      // Event listeners for custom file input previews and file name display
      document.getElementById('officerSignature').addEventListener('change', function(event) {
        displayImagePreview(event.target, 'officerSignaturePreview');
        document.getElementById('officerSignatureFileName').textContent = this.files.length > 0 ? this.files[0].name : 'No file chosen';
        validateField(document.getElementById('officerSignature')); // Trigger validation
      });
      document.getElementById('officeSeal').addEventListener('change', function(event) {
        displayImagePreview(event.target, 'officeSealPreview');
        document.getElementById('officeSealFileName').textContent = this.files.length > 0 ? this.files[0].name : 'No file chosen';
        validateField(document.getElementById('officeSeal')); // Trigger validation
      });
      console.log('File input change listeners set.');

      // Event listeners for general validation on blur for text/select/textarea
      document.getElementById('cdrApplicationForm').querySelectorAll('input:not([type="file"]):not([type="checkbox"]), select, textarea').forEach(input => {
        input.addEventListener('blur', () => validateField(input));
      });
      console.log('Blur validation listeners set for non-file/checkbox inputs.');

      // Specific validation for crimeNo on input to give immediate feedback
      document.getElementById('crimeNo').addEventListener('input', function() {
        validateField(this);
      });
      console.log('Crime No input validation listener set.');
      console.log('InputForm.html initialization complete.');
    };

    /**
     * Populates dynamic fields (accused names, phone numbers) when loading existing data.
     * @param {string} containerId - The ID of the container div.
     * @param {string} inputClass - The class for the input field.
     * @param {string} nameAttribute - The 'name' attribute for the input.
     * @param {Array<string>} values - Array of values to populate.
     */
    function populateDynamicFields(containerId, inputClass, nameAttribute, values) {
      const container = document.getElementById(containerId);
      container.innerHTML = ''; // Clear existing fields
      console.log(`Populating dynamic fields for ${containerId} with values:`, values);

      // Ensure 'values' is an array; if null/undefined or empty, treat as [''] to add one empty field
      const valuesToPopulate = (values && Array.isArray(values) && values.length > 0) ? values : [''];

      valuesToPopulate.forEach(value => {
        addDynamicField(containerId, inputClass, inputClass.includes('accused') ? 'Name of Accused' : 'Phone No/ IMEI No', value);
      });
    }

    /**
     * Displays a preview of the selected image file.
     * @param {HTMLInputElement} fileInput - The file input element.
     * @param {string} previewContainerId - The ID of the div to display the preview.
     */
    function displayImagePreview(fileInput, previewContainerId) {
      const previewContainer = document.getElementById(previewContainerId);
      const imgElement = previewContainer.querySelector('img');
      const spanElement = previewContainer.querySelector('span');
      console.log(`Displaying image preview for ${fileInput.id}`);

      if (fileInput.files && fileInput.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
          imgElement.src = e.target.result;
          imgElement.style.display = 'block';
          if (spanElement) spanElement.style.display = 'none';
        };
        reader.readAsDataURL(fileInput.files[0]);
      } else {
        imgElement.src = '';
        imgElement.style.display = 'none';
        if (spanElement) spanElement.style.display = 'block';
      }
    }

    // Button handler functions
    function handleSaveDraft() {
      const fakeEvent = { preventDefault: () => {} };
      handleSubmit(fakeEvent, 'Draft');
    }

    function handleSaveAndContinue() {
      const fakeEvent = { preventDefault: () => {} };
      handleSubmit(fakeEvent, 'DraftAndPreview');
    }
  </script>
</body>
</html>
