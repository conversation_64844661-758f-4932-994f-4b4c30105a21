/**
 * @file Code.gs
 * @description Google Apps Script backend for the CDR Applications System.
 * Handles data storage, PDF generation, file uploads, and email submission.
 */

// --- Constants ---
// REPLACE WITH YOUR ACTUAL GOOGLE SHEET ID
const SPREADSHEET_ID = '1hiWXCmmYjpxXVZL55GbkTIJAAJiu6E-NQbPNUoQe-3A';
const SHEET_NAME = 'Applications Data';
// REPLACE WITH YOUR ACTUAL GOOGLE DOC TEMPLATE ID
const TEMPLATE_DOC_ID = '1J3C8xViw2VPvaoc4WgzWSVMwAnP5b1NzuLfdwGCXhkU';
const PARENT_FOLDER_PATH = 'Cyber Cell/CDR Applications'; // Path within 'My Drive'
const SIG_FOLDER_NAME = 'Uploaded Signature';
const SEAL_FOLDER_NAME = 'Uploaded Seal';
const RECIPIENT_EMAIL = '<EMAIL>';
const CC_EMAIL = '<EMAIL>';

// Column headers in the Google Sheet (exact order)
const SHEET_HEADERS = [
  'Log Book No', 'Request Type', 'Police Station/ Office', 'Crime Type', 'Crime No',
  'Office Where Crime is Registered', 'Under Section', 'Date of Report', 'Date of Occurrence',
  'Place of Occurrence', 'Name of Complainant', 'Designation', 'Name of Accused',
  'Gist of the case', 'Phone No/ IMEI No', 'Period From', 'Period To',
  'Purpose of details sought', 'Submission Date', 'Submission Place',
  'Officer Signature ID', 'Office Seal ID', 'Forwarded to DC(x)', 'Status', 'Row Index'
];

// Dropdown options for InputForm.html
const DROPDOWN_OPTIONS = {
  policeStations: ["Excise Range Office, Kollam"],
  crimeTypes: ["NDPS", "ABKARI"],
  designations: ["Joint Excise Commissioner", "Deputy Excise Commissioner", "Assistant Excise Commissioner", "Circle Inspector of Excise", "Excise Inspector"],
  officeWhereCrimeIsRegisteredOptions: ["Excise Range Office, Kollam"]
};

// Helper map for converting sheet headers to formData keys easily
const SHEET_HEADERS_MAP = {
  logBookNo: 'Log Book No',
  requestType: 'Request Type',
  policeStation: 'Police Station/ Office',
  crimeType: 'Crime Type',
  crimeNo: 'Crime No',
  officeWhereCrimeIsRegistered: 'Office Where Crime is Registered',
  underSection: 'Under Section',
  dateOfReport: 'Date of Report',
  dateOfOccurrence: 'Date of Occurrence',
  placeOfOccurrence: 'Place of Occurrence',
  officerName: 'Name of Complainant',
  designation: 'Designation',
  nameOfAccused: 'Name of Accused',
  gistOfTheCase: 'Gist of the case',
  phoneNoIMEINo: 'Phone No/ IMEI No', // Corrected 'n' to lowercase
  fromDate: 'Period From',
  toDate: 'Period To',
  purposeOfDetailsSought: 'Purpose of details sought',
  submissionDate: 'Submission Date',
  submissionPlace: 'Submission Place',
  officerSignatureID: 'Officer Signature ID',
  officeSealID: 'Office Seal ID',
  forwardedToDCX: 'Forwarded to DC(x)',
  status: 'Status',
  rowIndex: 'Row Index'
};


/**
 * --- UI Functions (for showing HTML dialogs) ---
 */

/**
 * Triggered when the Google Sheet is opened. Displays the dashboard.
 */
function onOpen() {
  Logger.log('onOpen function started.');
  try {
    const ui = SpreadsheetApp.getUi();
    // Set width and height to cover the entire screen
    const htmlOutput = HtmlService.createHtmlOutputFromFile('Dashboard')
      .setWidth(1400) // Increased width for full-screen coverage
      .setHeight(900) // Increased height for full-screen coverage
      .setSandboxMode(HtmlService.SandboxMode.IFRAME);
    ui.showModalDialog(htmlOutput, 'CDR Applications Dashboard');
    Logger.log('Dashboard modal shown successfully.');
  } catch (e) {
    Logger.log('Error in onOpen: ' + e.message);
    SpreadsheetApp.getUi().alert('Error opening dashboard. Please try again or contact support.');
  }
}

/**
 * Opens the InputForm.html as a modal dialog.
 */
function showInputForm(rowIndex) {
  try {
    const html = HtmlService.createHtmlOutputFromFile('InputForm')
      .setWidth(1400) // Increased width for full-screen coverage
      .setHeight(900) // Increased height for full-screen coverage
      .setTitle('CDR Application Form');
    SpreadsheetApp.getUi().showModalDialog(html, 'CDR Application Form');
  } catch (error) {
    Logger.log('Error showing input form: ' + error.toString());
    SpreadsheetApp.getUi().alert('Failed to show input form. Please try again.');
  }
}

/**
 * Opens the Preview.html as a modal dialog.
 */
function showPreview(rowIndex) {
  try {
    const html = HtmlService.createHtmlOutputFromFile('Preview')
      .setWidth(1400) // Increased width for full-screen coverage
      .setHeight(900) // Increased height for full-screen coverage
      .setTitle('Application Preview');
    SpreadsheetApp.getUi().showModalDialog(html, 'Application Preview');
  } catch (error) {
    Logger.log('Error showing preview: ' + error.toString());
    SpreadsheetApp.getUi().alert('Failed to show preview. Please try again.');
  }
}

/**
 * Opens a new HTML modal to display filtered applications based on status.
 * This is a placeholder for a new HTML file (e.g., FilteredApplicationsList.html)
 * that would list applications. The client-side will pass the filter status.
 *
 * NOTE: The actual FilteredApplicationsList.html and its data loading logic
 * would need to be implemented by the AI. This function just opens the modal.
 * @param {string} statusFilter - The status to filter applications by (e.g., 'Draft', 'Pending').
 */
function showFilteredApplicationsList(status) {
  try {
    const html = HtmlService.createHtmlOutputFromFile('FilteredApplicationsList')
      .setWidth(1400) // Increased width for full-screen coverage
      .setHeight(900) // Increased height for full-screen coverage
      .setTitle('Applications List - ' + status);
    SpreadsheetApp.getUi().showModalDialog(html, 'Applications List');
  } catch (error) {
    Logger.log('Error showing filtered list: ' + error.toString());
    SpreadsheetApp.getUi().alert('Failed to show applications list. Please try again.');
  }
}


/**
 * Closes the current active modal (InputForm or Preview) and re-opens the Dashboard.
 * This is called from client-side (e.g., InputForm.html after "Save Draft" or "Cancel").
 */
function closeFormAndShowDashboard() {
  Logger.log('closeFormAndShowDashboard function started.');
  try {
    return { success: true, message: "Returning to dashboard." };
  } catch (e) {
    Logger.log('Error in closeFormAndShowDashboard: ' + e.message);
    throw new Error('Failed to return to dashboard: ' + e.message);
  }
}

/**
 * --- Data Retrieval Functions ---
 */

/**
 * Retrieves all data from the "Applications Data" sheet and calculates counts for dashboard.
 * @returns {object} An object containing application counts.
 */
function getDashboardData() {
  Logger.log('getDashboardData function started.');
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName(SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${SHEET_NAME}" not found.`);
    }

    const data = sheet.getDataRange().getValues();
    if (data.length <= 1) { // Only header row or empty sheet
      Logger.log('Sheet is empty or only contains headers. Returning empty dashboard data.');
      return {
        total: 0, allSubmitted: 0, pending: 0, approved: 0, rejected: 0, delivered: 0, draft: 0
      };
    }

    const headers = data[0];
    const applications = data.slice(1); // Exclude header row

    // Get column indices dynamically
    const statusColumnIndex = headers.indexOf('Status');

    if (statusColumnIndex === -1) {
      const missingHeaders = SHEET_HEADERS.filter(h => headers.indexOf(h) === -1);
      throw new Error('Required columns are missing in the Google Sheet: ' + missingHeaders.join(', ') + '. Please ensure first row matches: ' + SHEET_HEADERS.join(', '));
    }

    let total = 0;
    let pending = 0;
    let approved = 0;
    let rejected = 0;
    let delivered = 0;
    let draft = 0;

    applications.forEach((row) => {
      total++;
      const status = row[statusColumnIndex];
      switch (status) {
        case 'Pending': pending++; break;
        case 'Approved': approved++; break;
        case 'Rejected': rejected++; break;
        case 'Delivered': delivered++; break; // New status for dashboard
        case 'Draft': draft++; break;
      }
    });

    const allSubmitted = pending + approved + rejected + delivered; // All non-draft applications

    Logger.log(`Dashboard data fetched: Total=${total}, AllSubmitted=${allSubmitted}, Pending=${pending}, Approved=${approved}, Rejected=${rejected}, Delivered=${delivered}, Drafts=${draft}`);
    return { total, allSubmitted, pending, approved, rejected, delivered, draft };
  } catch (e) {
    Logger.log('Error in getDashboardData: ' + e.message + ' Stack: ' + e.stack);
    throw new Error('Failed to retrieve dashboard data: ' + e.message);
  }
}

/**
 * Returns a JSON object containing arrays for dropdown options.
 * @returns {object} An object with policeStations, crimeTypes, and designations arrays.
 */
function getDropdownOptions() {
  Logger.log('getDropdownOptions function started.');
  return DROPDOWN_OPTIONS;
}

/**
 * Retrieves applications filtered by status.
 * @param {string} status - The status to filter by (e.g., 'Draft', 'Pending', 'Approved', 'Rejected', 'Delivered').
 * @returns {Array} Array of application objects matching the status.
 */
function getFilteredApplications(status) {
  Logger.log(`getFilteredApplications function started for status: ${status}.`);
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName(SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${SHEET_NAME}" not found.`);
    }

    const data = sheet.getDataRange().getValues();
    if (data.length <= 1) { // Only header row or empty sheet
      Logger.log('Sheet is empty or only contains headers. Returning empty array.');
      return [];
    }

    const headers = data[0];
    const applications = data.slice(1); // Exclude header row

    // Get column indices dynamically
    const statusColumnIndex = headers.indexOf('Status');
    const rowIndexColumnIndex = headers.indexOf('Row Index');

    if (statusColumnIndex === -1 || rowIndexColumnIndex === -1) {
      throw new Error('Required columns are missing in the Google Sheet. Please ensure first row matches: ' + SHEET_HEADERS.join(', '));
    }

    // Filter applications by status
    let filteredApplications;

    if (status === 'AllSubmitted') {
      // For "AllSubmitted", include all non-draft applications
      filteredApplications = applications
        .filter(row => row[statusColumnIndex] !== 'Draft' && row[statusColumnIndex] !== '')
        .map((row, index) => {
          const application = {};
          headers.forEach((header, colIndex) => {
            if (header === 'Row Index') {
              application[header] = row[colIndex];
            } else if (header === 'Name of Accused' || header === 'Phone No/ IMEI No' || header === 'Request Type') {
              // Split comma-separated strings into arrays
              application[header] = row[colIndex] ? row[colIndex].split(',').map(item => item.trim()) : [];
            } else if (header.includes('Date')) {
              // Format dates as YYYY-MM-DD
              application[header] = row[colIndex] ? new Date(row[colIndex]).toISOString().split('T')[0] : '';
            } else {
              application[header] = row[colIndex];
            }
          });
          return application;
        });
    } else {
      // For specific status, filter by exact match
      filteredApplications = applications
        .filter(row => row[statusColumnIndex] === status)
        .map((row, index) => {
          const application = {};
          headers.forEach((header, colIndex) => {
            if (header === 'Row Index') {
              application[header] = row[colIndex];
            } else if (header === 'Name of Accused' || header === 'Phone No/ IMEI No' || header === 'Request Type') {
              // Split comma-separated strings into arrays
              application[header] = row[colIndex] ? row[colIndex].split(',').map(item => item.trim()) : [];
            } else if (header.includes('Date')) {
              // Format dates as YYYY-MM-DD
              application[header] = row[colIndex] ? new Date(row[colIndex]).toISOString().split('T')[0] : '';
            } else {
              application[header] = row[colIndex];
            }
          });
          return application;
        });
    }

    Logger.log(`Found ${filteredApplications.length} applications with status: ${status}`);
    return filteredApplications;
  } catch (e) {
    Logger.log('Error in getFilteredApplications: ' + e.message + ' Stack: ' + e.stack);
    // Return empty array instead of throwing error to prevent client-side crashes
    return [];
  }
}

/**
 * --- Data Manipulation Functions ---
 */

/**
 * Saves or updates application data in the Google Sheet.
 * @param {object} formData - Object containing form data.
 * @param {string} status - 'Draft' or 'Pending'.
 * @param {number|null} rowIndex - 1-based row index for updating an existing row, null for new.
 * @returns {object} Success/error status and the rowIndex of the saved/updated row, plus updated formData.
 */
function saveApplication(formData, status, rowIndex = null) {
  Logger.log(`saveApplication function started for rowIndex: ${rowIndex}, status: ${status}`);
  Logger.log('Received formData: ' + JSON.stringify(formData));
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName(SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${SHEET_NAME}" not found. Please ensure the sheet ID and name are correct.`);
    }

    // Ensure headers are in the sheet, add if not present (for first run)
    let sheetHeadersInUse = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    if (sheetHeadersInUse.join(',') !== SHEET_HEADERS.join(',')) { // Simple check for header match
      sheet.getRange(1, 1, 1, SHEET_HEADERS.length).setValues([SHEET_HEADERS]);
      sheetHeadersInUse = SHEET_HEADERS; // Use predefined headers for consistent mapping
      Logger.log('Sheet headers updated to match expected structure.');
    }

    const headerMap = new Map(sheetHeadersInUse.map((h, i) => [h, i])); // Map header name to column index for robust lookup

    const rowData = new Array(sheetHeadersInUse.length).fill(''); // Initialize with empty strings for all expected columns

    // IMPORTANT FIX: Ensure rowIndex is a number, as it might be passed as a string from client-side sessionStorage
    const actualRowIndex = (rowIndex !== null && typeof rowIndex === 'string') ? parseInt(rowIndex, 10) : rowIndex;

    // Populate rowData based on form data and sheet headers
    for (const formKey in formData) {
      if (formData.hasOwnProperty(formKey)) {
        const sheetHeader = SHEET_HEADERS_MAP[formKey];
        if (sheetHeader) {
          const colIndex = headerMap.get(sheetHeader);
          if (colIndex !== undefined) {
            let value = formData[formKey];

            // Handle arrays for storage (comma-separated string)
            if (Array.isArray(value)) {
              value = value.join(', ');
            }
            // Convert date strings (YYYY-MM-DD from flatpickr) to actual Date objects for Sheets
            if (['dateOfReport', 'dateOfOccurrence', 'fromDate', 'toDate', 'submissionDate'].includes(formKey) && value) {
                try {
                    value = new Date(value); // Attempt to parse-MM-DD string into a Date object
                    if (isNaN(value.getTime())) { // Check for invalid date
                        value = ''; // Set to empty if parsing results in an invalid date
                    }
                } catch (e) {
                    Logger.log(`Warning: Could not parse date for ${formKey}: ${value}. Error: ${e.message}`);
                    value = ''; // Set to empty if parsing fails
                }
            }
            rowData[colIndex] = value === null || value === undefined ? '' : value; // Ensure null/undefined values are empty strings
          }
        }
      }
    }

    // Handle image uploads if base64 data is present
    const officerSignatureIDColIndex = headerMap.get('Officer Signature ID');
    const officeSealIDColIndex = headerMap.get('Office Seal ID');

    if (formData.officerSignatureBase64) {
      const sigFolder = getOrCreateFolder(SIG_FOLDER_NAME, PARENT_FOLDER_PATH);
      const officerNameForFile = formData.officerName ? formData.officerName.replace(/[^a-zA-Z0-9]/g, '_') : 'Officer'; // Sanitize name
      const signatureBlob = Utilities.newBlob(Utilities.base64Decode(formData.officerSignatureBase64), 'image/png', `${officerNameForFile}_${Date.now()}.png`);
      const uploadedFile = sigFolder.createFile(signatureBlob);
      rowData[officerSignatureIDColIndex] = uploadedFile.getId();
      formData.officerSignatureID = uploadedFile.getId(); // Store in formData for preview on client-side
      Logger.log('Officer Signature uploaded. ID: ' + uploadedFile.getId());
    } else if (formData.officerSignatureID) {
      // If no new image, but ID exists (for editing), keep it
      rowData[officerSignatureIDColIndex] = formData.officerSignatureID;
      Logger.log('Retaining existing Officer Signature ID: ' + formData.officerSignatureID);
    } else {
      rowData[officerSignatureIDColIndex] = ''; // Ensure it's cleared if no image
      formData.officerSignatureID = ''; // Also clear in formData
    }

    if (formData.officeSealBase64) {
      const sealFolder = getOrCreateFolder(SEAL_FOLDER_NAME, PARENT_FOLDER_PATH);
      const sealBlob = Utilities.newBlob(Utilities.base64Decode(formData.officeSealBase64), 'image/png', `OfficeSeal_${Date.now()}.png`);
      const uploadedFile = sealFolder.createFile(sealBlob);
      rowData[officeSealIDColIndex] = uploadedFile.getId();
      formData.officeSealID = uploadedFile.getId(); // Store in formData for preview on client-side
      Logger.log('Office Seal uploaded. ID: ' + uploadedFile.getId());
    } else if (formData.officeSealID) {
      // If no new image, but ID exists (for editing), keep it
      rowData[officeSealIDColIndex] = formData.officeSealID;
      Logger.log('Retaining existing Office Seal ID: ' + formData.officeSealID);
    } else {
      rowData[officeSealIDColIndex] = ''; // Ensure it's cleared if no image
      formData.officeSealID = ''; // Also clear in formData
    }

    // Set Status and Forwarded to DC(x)
    const statusColIndex = headerMap.get('Status');
    const forwardedColIndex = headerMap.get('Forwarded to DC(x)');
    const rowIndexColIndex = headerMap.get('Row Index');

    rowData[statusColIndex] = status;
    if (status === 'Pending') { // 'Pending' status is set during final submission from Preview.html
      rowData[forwardedColIndex] = new Date(); // Current timestamp
    } else { // 'Draft' status
      rowData[forwardedColIndex] = ''; // Clear if not pending
    }

    let targetRow;
    if (actualRowIndex && typeof actualRowIndex === 'number' && actualRowIndex >= 2 && actualRowIndex <= sheet.getLastRow()) {
      // Update existing row
      targetRow = actualRowIndex;
      sheet.getRange(targetRow, 1, 1, rowData.length).setValues([rowData]);
      Logger.log('Row updated at index: ' + targetRow);
    } else {
      // Append new row
      sheet.appendRow(rowData);
      targetRow = sheet.getLastRow();
      Logger.log('New row appended at index: ' + targetRow);
    }

    // Update the 'Row Index' column with the actual 1-based row index
    // Note: column indices are 0-based for headerMap, but 1-based for sheet.getRange().setValue()
    sheet.getRange(targetRow, rowIndexColIndex + 1).setValue(targetRow);
    formData.rowIndex = targetRow; // Update formData with actual row index for client

    Logger.log('Application saved/updated successfully. Final row index: ' + targetRow);
    return { success: true, message: 'Application saved successfully!', rowIndex: targetRow, formData: formData };
  } catch (e) {
    Logger.log('Error in saveApplication: ' + e.message + ' Stack: ' + e.stack);
    // Return a structured error to the client
    return { success: false, message: 'Failed to save application: ' + e.message };
  }
}

/**
 * Loads a specific application's data from the sheet for editing.
 * @param {number} rowIndex - The 1-based row index of the application.
 * @returns {object} The application data as a JSON object.
 */
function loadApplication(rowIndex) {
  Logger.log('loadApplication function started for rowIndex: ' + rowIndex);
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName(SHEET_NAME);
    if (!sheet || rowIndex < 2 || rowIndex > sheet.getLastRow()) {
      throw new Error(`Invalid row index (${rowIndex}) or sheet not found/too small.`);
    }

    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const rowData = sheet.getRange(rowIndex, 1, 1, headers.length).getValues()[0];

    Logger.log('DEBUG: loadApplication - Full rowData from sheet: ' + JSON.stringify(rowData));

    const appData = {};
    headers.forEach((header, index) => {
      // Find the formKey corresponding to the sheet header
      const formKey = Object.keys(SHEET_HEADERS_MAP).find(key => SHEET_HEADERS_MAP[key] === header);
      if (formKey) {
        let value = rowData[index];

        // --- START Diagnostic logging for phoneNoIMEINo ---
        if (formKey === 'phoneNoIMEINo') {
          Logger.log(`DEBUG: loadApplication - Raw value for ${header} from sheet at index ${index}: "${value}" (Type: ${typeof value})`);
        }
        // --- END Diagnostic logging ---

        // Convert comma-separated strings back to arrays, specifically for dynamic fields
        if (['nameOfAccused', 'phoneNoIMEINo', 'requestType'].includes(formKey)) {
          if (typeof value !== 'string' || value === null || value === undefined) {
            value = '';
          }
          // --- START Diagnostic logging for phoneNoIMEINo processing ---
          if (formKey === 'phoneNoIMEINo') {
            Logger.log(`DEBUG: loadApplication - Before split/filter, value is: "${value}"`);
          }
          // --- END Diagnostic logging ---
          value = value.split(',').map(s => s.trim()).filter(s => s !== ''); // Corrected split for comma only, trim, and filter out empty strings

          // --- START Diagnostic logging for phoneNoIMEINo processing ---
          if (formKey === 'phoneNoIMEINo') {
            Logger.log(`DEBUG: loadApplication - After split/filter, value is: ${JSON.stringify(value)} (Length: ${value.length})`);
          }
          // --- END Diagnostic logging ---

          // Ensure it's not an empty array if the original was empty (to allow adding one empty field on load)
          if (value.length === 0 && (formKey === 'nameOfAccused' || formKey === 'phoneNoIMEINo')) {
              value = [''];
              // --- START Diagnostic logging for phoneNoIMEINo processing ---
              if (formKey === 'phoneNoIMEINo') {
                Logger.log(`DEBUG: loadApplication - Adjusted empty array to [''] for display.`);
              }
              // --- END Diagnostic logging ---
          }
        }
        // Format Date objects to YYYY-MM-DD strings for HTML input type="date"
        else if (value instanceof Date && !isNaN(value.getTime())) { // Check if it's a valid Date object
          value = Utilities.formatDate(value, Session.getScriptTimeZone(), 'yyyy-MM-dd');
        } else if (value === null || value === undefined) {
          value = '';
        }
        appData[formKey] = value;
      }
    });

    // Add rowIndex for client-side tracking
    appData.rowIndex = rowIndex;
    Logger.log('Application data loaded for row ' + rowIndex + '. Final appData: ' + JSON.stringify(appData));
    return { success: true, appData: appData };
  } catch (e) {
    Logger.log('Error in loadApplication: ' + e.message + ' Stack: ' + e.stack);
    return { success: false, message: 'Failed to load application data: ' + e.message + '. Please check sheet data for row ' + rowIndex };
  }
}

/**
 * --- PDF Generation and Email Sending Functions ---
 */

/**
 * Replaces a specific text placeholder in the document body with an image.
 * The image will be inserted at the location of the text placeholder, and the placeholder text removed.
 * @param {GoogleAppsScript.Document.Body} body - The body of the Google Document.
 * @param {string} placeholderText - The exact text string of the placeholder (e.g., '{{officerSignature}}').
 * @param {string} imageId - The Google Drive File ID of the image to insert.
 * @param {number} [width=150] - Desired width for the inserted image.
 * @param {number} [height=75] - Desired height for the inserted image.
 * @returns {boolean} True if replacement was successful, false otherwise.
 */
function replaceTextPlaceholderWithImage(body, placeholderText, imageId, width = 150, height = 75) {
  Logger.log(`Attempting to replace "${placeholderText}" with image ID: ${imageId}`);

  if (!imageId) {
    Logger.log(`No image ID provided for placeholder: ${placeholderText}. Skipping image insertion.`);
    return false;
  }

  try {
    const imageFile = DriveApp.getFileById(imageId);
    const imageBlob = imageFile.getBlob();
    Logger.log(`Image blob obtained for ID: ${imageId}. Blob type: ${imageBlob.getContentType()}`);

    let found = false;
    let searchResult = body.findText(placeholderText);

    // Loop to find and replace all occurrences
    while (searchResult) {
      const element = searchResult.getElement();
      if (element && element.getType() === DocumentApp.ElementType.TEXT) {
        const textElement = element.asText();
        const startOffset = searchResult.getStartOffset();
        const endOffsetInclusive = searchResult.getEndOffsetInclusive();

        const parent = textElement.getParent();
        const index = parent.getChildIndex(textElement);

        // Remove the text placeholder
        textElement.deleteText(startOffset, endOffsetInclusive);

        // Insert the image at the same position
        const insertedImage = parent.insertImage(index, imageBlob);
        insertedImage.setWidth(width).setHeight(height);
        Logger.log(`Inserted image for "${placeholderText}".`);
        found = true;

        // Continue searching from the next character after the deleted placeholder
        searchResult = body.findText(placeholderText); // Re-find from beginning as content changed
      } else {
        Logger.log(`Element for "${placeholderText}" was not a TEXT element or was null. Type: ${element ? element.getType() : 'null'}`);
        // Advance the search if the element isn't text to prevent infinite loops on non-text elements
        searchResult = body.findText(placeholderText, searchResult);
      }
    }
    return found;

  } catch (e) {
    Logger.log(`Error replacing text placeholder "${placeholderText}" with image ID ${imageId}: ${e.message}. Stack: ${e.stack}`);
    if (e.message.includes("not found") || e.message.includes("permission")) {
      Logger.log(`Verify image ID: ${imageId} exists and is accessible by the script.`);
    }
    return false;
  }
}

/**
 * Creates a temporary Google Doc, fills it with formData, converts it to PDF,
 * and returns its Base64 representation for preview. The temporary doc is deleted.
 * @param {object} formData - Complete application data.
 * @returns {object} Success/error status and the base64 encoded PDF string.
 */
function generatePdfPreview(formData) {
  let tempDoc = null;
  Logger.log('generatePdfPreview function started.');
  Logger.log('Received formData for PDF generation: ' + JSON.stringify(formData));
  try {
    const templateDoc = DocumentApp.openById(TEMPLATE_DOC_ID);
    // Make a temporary copy of the template in the root folder, which will be deleted later.
    tempDoc = DriveApp.getFileById(templateDoc.getId()).makeCopy(`temp_cdr_preview_${Date.now()}`);
    const doc = DocumentApp.openById(tempDoc.getId());
    const body = doc.getBody();

    // Replace text placeholders
    for (const key in formData) {
      if (formData.hasOwnProperty(key)) {
        // Skip image IDs and dynamic arrays (nameOfAccused, phoneNoIMEINo, requestType)
        // as they are handled specifically or by join operations already.
        if (key === 'officerSignatureID' || key === 'officeSealID' ||
            key === 'officerSignatureBase64' || key === 'officeSealBase64' || // Also skip base64 data
            key === 'nameOfAccused' || key === 'phoneNoIMEINo' || key === 'requestType') {
          continue;
        }

        let valueToReplace = formData[key];

        // Format dates for display in PDF (DD-MM-YYYY)
        if (['dateOfReport', 'dateOfOccurrence', 'fromDate', 'toDate', 'submissionDate'].includes(key)) {
          try {
            const dateObj = new Date(valueToReplace);
            // Check if the date is valid before formatting
            if (!isNaN(dateObj.getTime())) {
              valueToReplace = Utilities.formatDate(dateObj, Session.getScriptTimeZone(), 'dd-MM-yyyy');
            } else {
              valueToReplace = ''; // Invalid date, set to empty string
            }
          } catch (e) {
            valueToReplace = ''; // Error in parsing, set to empty string
            Logger.log(`Warning: Date formatting failed for ${key}: ${formData[key]} - ${e.message}`);
          }
        } else if (valueToReplace === null || valueToReplace === undefined) {
          valueToReplace = ''; // Ensure null/undefined values are empty strings
        }
        body.replaceText(`{{${key}}}`, valueToReplace.toString());
        Logger.log(`Replaced placeholder {{${key}}} with "${valueToReplace}".`);
      }
    }

    // Specific handling for dynamic fields that were stored as comma-separated strings
    const nameOfAccusedValue = Array.isArray(formData.nameOfAccused) ? formData.nameOfAccused.join(', ') : (formData.nameOfAccused || '');
    body.replaceText('{{nameOfAccused}}', nameOfAccusedValue);
    Logger.log(`Replaced {{nameOfAccused}} with: ${nameOfAccusedValue}`);

    const phoneNoIMEINoValue = Array.isArray(formData.phoneNoIMEINo) ? formData.phoneNoIMEINo.join(', ') : (formData.phoneNoIMEINo || '');
    body.replaceText('{{phoneNoIMEINo}}', phoneNoIMEINoValue);
    Logger.log(`Replaced {{phoneNoIMEINo}} with: ${phoneNoIMEINoValue}`);

    const requestTypeValue = Array.isArray(formData.requestType) ? formData.requestType.join(', ') : (formData.requestType || '');
    body.replaceText('{{requestType}}', requestTypeValue);
    Logger.log(`Replaced {{requestType}} with: ${requestTypeValue}`);

    // Replace image placeholders with actual images
    // NOTE: The template placeholders are {{officerSignature}} and {{OfficeSeal}}, not their IDs.
    replaceTextPlaceholderWithImage(body, '{{officerSignature}}', formData.officerSignatureID, 150, 75);
    replaceTextPlaceholderWithImage(body, '{{OfficeSeal}}', formData.officeSealID, 100, 100);

    doc.saveAndClose();

    // Convert the temporary Google Doc to a PDF blob and then base64 encode it
    const pdfBlob = doc.getAs(MimeType.PDF);
    if (!pdfBlob) {
        throw new Error("Failed to create PDF blob from document (document might be empty or invalid).");
    }
    const base64Pdf = Utilities.base64Encode(pdfBlob.getBytes());
    Logger.log('PDF blob converted to base64 successfully for preview.');

    return { success: true, base64Pdf: base64Pdf };

  } catch (e) {
    Logger.log('Error in generatePdfPreview: ' + e.message + ' Stack: ' + e.stack);
    return { success: false, message: 'Failed to generate PDF preview: ' + e.message };
  } finally {
    if (tempDoc) {
      tempDoc.setTrashed(true); // Delete the temporary document file
      Logger.log('Temporary document for preview deleted.');
    }
  }
}

/**
 * Finalizes an application by updating its status in the sheet and sending an email with the PDF.
 * @param {object} formData - Complete application data.
 * @param {number} rowIndex - The 1-based row index in the sheet.
 * @returns {object} Success/error status.
 */
function submitApplication(formData, rowIndex) {
  let tempDoc = null;
  Logger.log(`submitApplication function started for rowIndex: ${rowIndex}`);
  Logger.log('Received formData for submission: ' + JSON.stringify(formData));
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName(SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${SHEET_NAME}" not found.`);
    }

    // Update Status to "Pending" and Forwarded to DC(x) timestamp
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const headerMap = new Map(headers.map((h, i) => [h, i]));

    const statusColIndex = headerMap.get('Status');
    const forwardedColIndex = headerMap.get('Forwarded to DC(x)');

    if (statusColIndex === undefined || forwardedColIndex === undefined) {
      throw new Error('Status or Forwarded to DC(x) column not found in sheet. Please check sheet headers.');
    }

    // Update the sheet row
    sheet.getRange(rowIndex, statusColIndex + 1).setValue('Pending');
    sheet.getRange(rowIndex, forwardedColIndex + 1).setValue(new Date());
    Logger.log('Sheet row ' + rowIndex + ' updated to Pending status.');

    // Generate the PDF for email attachment (reusing logic from generatePdfPreview)
    const templateDoc = DocumentApp.openById(TEMPLATE_DOC_ID);
    // Make a temporary copy of the template in the root folder, which will be deleted later.
    tempDoc = DriveApp.getFileById(templateDoc.getId()).makeCopy(`temp_cdr_email_${Date.now()}`);
    const doc = DocumentApp.openById(tempDoc.getId());
    const body = doc.getBody();
    Logger.log('Generating PDF for email submission.');

    // Replace text placeholders
    for (const key in formData) {
      if (formData.hasOwnProperty(key)) {
        // Skip image IDs and dynamic arrays (nameOfAccused, phoneNoIMEINo, requestType)
        // as they are handled specifically or by join operations already.
        if (key === 'officerSignatureID' || key === 'officeSealID' ||
            key === 'officerSignatureBase64' || key === 'officeSealBase64' || // Also skip base64 data
            key === 'nameOfAccused' || key === 'phoneNoIMEINo' || key === 'requestType') {
          continue;
        }

        let valueToReplace = formData[key];

        // Format dates for display in PDF (DD-MM-YYYY)
        if (['dateOfReport', 'dateOfOccurrence', 'fromDate', 'toDate', 'submissionDate'].includes(key)) {
          try {
            const dateObj = new Date(valueToReplace);
            if (!isNaN(dateObj.getTime())) {
              valueToReplace = Utilities.formatDate(dateObj, Session.getScriptTimeZone(), 'dd-MM-yyyy');
            } else {
              valueToReplace = '';
            }
          } catch (e) {
            valueToReplace = '';
            Logger.log(`Warning: Date formatting failed for ${key} during submission PDF: ${formData[key]} - ${e.message}`);
          }
        } else if (valueToReplace === null || valueToReplace === undefined) {
          valueToReplace = '';
        }
        body.replaceText(`{{${key}}}`, valueToReplace.toString());
        Logger.log(`Replaced placeholder {{${key}}} with "${valueToReplace}" for email PDF.`);
      }
    }

    // Specific handling for dynamic fields that were stored as comma-separated strings
    const nameOfAccusedValue = Array.isArray(formData.nameOfAccused) ? formData.nameOfAccused.join(', ') : (formData.nameOfAccused || '');
    body.replaceText('{{nameOfAccused}}', nameOfAccusedValue);
    Logger.log(`Replaced {{nameOfAccused}} with: ${nameOfAccusedValue} for email PDF.`);

    const phoneNoIMEINoValue = Array.isArray(formData.phoneNoIMEINo) ? formData.phoneNoIMEINo.join(', ') : (formData.phoneNoIMEINo || '');
    body.replaceText('{{phoneNoIMEINo}}', phoneNoIMEINoValue);
    Logger.log(`Replaced {{phoneNoIMEINo}} with: ${phoneNoIMEINoValue} for email PDF.`);

    const requestTypeValue = Array.isArray(formData.requestType) ? formData.requestType.join(', ') : (formData.requestType || '');
    body.replaceText('{{requestType}}', requestTypeValue);
    Logger.log(`Replaced {{requestType}} with: ${requestTypeValue} for email PDF.`);

    // Replace image placeholders with actual images
    // NOTE: The template placeholders are {{officerSignature}} and {{OfficeSeal}}, not their IDs.
    replaceTextPlaceholderWithImage(body, '{{officerSignature}}', formData.officerSignatureID, 150, 75);
    replaceTextPlaceholderWithImage(body, '{{OfficeSeal}}', formData.officeSealID, 100, 100);

    doc.saveAndClose();
    const pdfBlob = doc.getAs(MimeType.PDF);
    // Ensure filename includes log book number if available, otherwise a generic name
    pdfBlob.setName(`CDR Application - ${formData.logBookNo || 'N/A'}-${formData.crimeNo || 'N/A'}.pdf`);
    Logger.log('PDF prepared for email attachment with name: ' + pdfBlob.getName());

    // Format email subject
    const requestTypeFormatted = Array.isArray(formData.requestType) ? formData.requestType.join(', ') : formData.requestType || 'N/A';
    const crimeNoFormatted = formData.crimeNo || 'N/A';
    const officeWhereCrimeIsRegisteredFormatted = formData.officeWhereCrimeIsRegistered || 'N/A';
    const phoneIMEIFormatted = Array.isArray(formData.phoneNoIMEINo) && formData.phoneNoIMEINo.length > 0 ? formData.phoneNoIMEINo[0] : 'N/A';
    const subject = `[${requestTypeFormatted}] - [${formData.crimeType || 'N/A'}] CR ${crimeNoFormatted} Of ${officeWhereCrimeIsRegisteredFormatted} - ${phoneIMEIFormatted}`;
    Logger.log('Email subject: ' + subject);

    // Send email
    GmailApp.sendEmail(
      RECIPIENT_EMAIL,
      subject,
      'Please find the attached CDR application.',
      {
        cc: CC_EMAIL,
        attachments: [pdfBlob]
      }
    );
    Logger.log('Email sent successfully to ' + RECIPIENT_EMAIL + (CC_EMAIL ? ' and CC ' + CC_EMAIL : ''));

    return { success: true, message: 'Application submitted and email sent successfully!' };

  } catch (e) {
    Logger.log('Error in submitApplication: ' + e.message + ' Stack: ' + e.stack);
    return { success: false, message: 'Failed to submit application: ' + e.message };
  } finally {
    if (tempDoc) {
      tempDoc.setTrashed(true); // Delete the temporary document
      Logger.log('Temporary document for submission deleted.');
    }
  }
}

/**
 * Submits the application, updates status to 'Pending', generates PDF, and sends to emails.
 * @param {number} rowIndex - The 1-based row index of the application.
 * @returns {object} Success/error status.
 */
function submitAndSendApplication(rowIndex) {
  try {
    // Load application data
    const appData = loadApplication(rowIndex);
    if (!appData) {
      return { success: false, message: 'Application not found.' };
    }
    // Update status to 'Pending'
    saveApplication(appData, 'Pending', rowIndex);
    // Generate PDF
    const pdfResult = generatePdfPreview(appData, rowIndex);
    if (!pdfResult.success) {
      return { success: false, message: pdfResult.message };
    }
    // Send email
    const userEmail = Session.getActiveUser().getEmail();
    const subject = 'CDR Application Submitted';
    const body = 'Please find the attached CDR application PDF.';
    const attachments = [pdfResult.pdfFile];
    MailApp.sendEmail({
      to: RECIPIENT_EMAIL,
      cc: `${CC_EMAIL},${userEmail}`,
      subject: subject,
      body: body,
      attachments: attachments
    });
    return { success: true };
  } catch (e) {
    Logger.log('Error in submitAndSendApplication: ' + e.message + ' Stack: ' + e.stack);
    return { success: false, message: 'Failed to submit and send: ' + e.message };
  }
}

/**
 * --- Helper Functions ---
 */

/**
 * Finds an existing Google Drive folder or creates it if it doesn't exist.
 * Can also create a nested path.
 * @param {string} folderName - The name of the folder to find/create.
 * @param {string} [parentPath] - Optional. The path to the parent folder (e.g., 'My Drive/Path/To/Folder').
 * @returns {GoogleAppsScript.Drive.Folder} The found or created folder object.
 */
function getOrCreateFolder(folderName, parentPath = null) {
  Logger.log(`getOrCreateFolder function started for folder: ${folderName}, parent: ${parentPath}`);
  let currentFolder = DriveApp.getRootFolder(); // Start from My Drive

  if (parentPath) {
    const pathParts = parentPath.split('/').map(part => part.trim()).filter(Boolean);
    for (const part of pathParts) {
      const folders = currentFolder.getFoldersByName(part);
      if (folders.hasNext()) {
        currentFolder = folders.next();
        Logger.log(`Found parent folder part: ${part}`);
      } else {
        currentFolder = currentFolder.createFolder(part);
        Logger.log(`Created parent folder part: ${part}`);
      }
    }
  }

  const folders = currentFolder.getFoldersByName(folderName);
  if (folders.hasNext()) {
    Logger.log(`Found target folder: ${folderName}`);
    return folders.next();
  } else {
    Logger.log(`Created target folder: ${folderName}`);
    return currentFolder.createFolder(folderName);
  }
}
