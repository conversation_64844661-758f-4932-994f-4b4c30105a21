<!DOCTYPE html>
<html>
<head>
  <base target="_top">
  <title>Filtered Applications List</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* General styles */
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f0f2f5;
      color: #333;
      display: flex;
      flex-direction: column;
      width: 100vw; /* Full viewport width */
      height: 100vh; /* Full viewport height */
      box-sizing: border-box;
    }

    /* Header */
    .header {
      background-color: #ffffff;
      padding: 20px 30px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      color: #1a73e8;
      font-size: 1.8rem;
      font-weight: 600;
      margin: 0;
    }

    .header .status-badge {
      background-color: #e8f0fe;
      color: #1a73e8;
      padding: 8px 16px;
      border-radius: 20px;
      font-weight: 500;
      font-size: 0.9rem;
    }

    .header .header-actions {
      display: flex;
      align-items: center;
    }

    .header .btn {
      background-color: #1a73e8;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 1rem;
      transition: background-color 0.3s ease;
    }

    .header .btn-secondary {
      background-color: #f0f0f0;
      color: #333;
      margin-left: 10px;
    }

    /* Main container */
    .list-container {
      flex-grow: 1;
      padding: 30px;
      max-width: 1200px;
      margin: 0 auto;
      width: 100%;
      box-sizing: border-box;
    }

    /* Applications Grid */
    .applications-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    /* Application Card */
    .application-card {
      background-color: #ffffff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      padding: 20px;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      cursor: pointer;
    }

    .application-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }

    .application-card h3 {
      color: #1a73e8;
      font-size: 1.2rem;
      margin: 0 0 15px 0;
      font-weight: 600;
    }

    .application-card .details {
      display: grid;
      gap: 10px;
    }

    .application-card .detail-item {
      display: flex;
      flex-direction: column;
    }

    .application-card .detail-label {
      font-size: 0.8rem;
      color: #666;
      margin-bottom: 2px;
    }

    .application-card .detail-value {
      font-size: 0.95rem;
      color: #333;
      font-weight: 500;
    }

    .application-card .status {
      margin-top: 15px;
      padding: 6px 12px;
      border-radius: 15px;
      font-size: 0.85rem;
      font-weight: 500;
      text-align: center;
      display: inline-block;
    }

    .application-card .status.pending { background-color: #fef7e0; color: #f4b400; }
    .application-card .status.approved { background-color: #e6f4ea; color: #34a853; }
    .application-card .status.rejected { background-color: #fce8e6; color: #ea4335; }
    .application-card .status.delivered { background-color: #e8f0fe; color: #1a73e8; }

    /* Empty state */
    .empty-state {
      text-align: center;
      padding: 40px;
      background-color: #ffffff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .empty-state h2 {
      color: #666;
      font-size: 1.4rem;
      margin-bottom: 10px;
    }

    .empty-state p {
      color: #888;
      margin-bottom: 20px;
    }

    /* Loader and Message Box */
    .loader-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      visibility: hidden;
      opacity: 0;
      transition: visibility 0s, opacity 0.3s linear;
    }

    .loader-overlay.show {
      visibility: visible;
      opacity: 1;
    }

    .spinner {
      border: 8px solid #f3f3f3;
      border-top: 8px solid #1a73e8;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .message-box {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: #ffffff;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
      z-index: 1001;
      text-align: center;
      min-width: 300px;
      max-width: 90%;
      visibility: hidden;
      opacity: 0;
      transition: visibility 0s, opacity 0.3s linear;
    }

    .message-box.show {
      visibility: visible;
      opacity: 1;
    }

    .message-box p {
      font-size: 1.1rem;
      margin-bottom: 20px;
      color: #333;
    }

    .message-box button {
      background-color: #1a73e8;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 1rem;
      transition: background-color 0.3s ease;
    }

    .message-box button:hover {
      background-color: #1764cf;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .header {
        padding: 15px 20px;
      }

      .header h1 {
        font-size: 1.5rem;
      }

      .list-container {
        padding: 20px;
      }

      .applications-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="loader-overlay" id="loader">
    <div class="spinner"></div>
  </div>

  <div class="message-box" id="messageBox">
    <p id="messageText"></p>
    <button id="messageBoxClose">OK</button>
  </div>

  <div class="header">
    <h1>Applications List</h1>
    <div class="status-badge" id="statusBadge">All</div>
    <div class="header-actions">
      <button type="button" class="btn btn-secondary" onclick="handleBackToDashboard()">Back to Dashboard</button>
    </div>
  </div>

  <div class="list-container">
    <div class="applications-grid" id="applicationsGrid">
      <!-- Application cards will be dynamically populated here -->
    </div>
  </div>

  <script>
    // Custom Message Box and Loader functions
    const loader = document.getElementById('loader');
    const messageBox = document.getElementById('messageBox');
    const messageText = document.getElementById('messageText');
    const messageBoxClose = document.getElementById('messageBoxClose');
    const statusBadge = document.getElementById('statusBadge');
    const applicationsGrid = document.getElementById('applicationsGrid');

    function showLoader() {
      loader.classList.add('show');
    }

    function hideLoader() {
      loader.classList.remove('show');
    }

    function showMessageBox(message, callback) {
      messageText.textContent = message;
      messageBox.classList.add('show');
      messageBoxClose.onclick = () => {
        hideMessageBox();
        if (callback) callback();
      };
    }

    function hideMessageBox() {
      messageBox.classList.remove('show');
    }

    function onFailure(error) {
      hideLoader();
      console.error("Error:", error);
      showMessageBox('An error occurred: ' + (error.message || error));
    }

    function createApplicationCard(application) {
      console.log('Creating card for application:', application);
      const card = document.createElement('div');
      card.className = 'application-card';

      // Safe access to properties with fallbacks
      const logBookNo = application["Log Book No"] || 'No Log Book Number';
      const policeStation = application["Police Station/ Office"] || 'N/A';
      const crimeNo = application["Crime No"] || 'N/A';
      const complainant = application["Name of Complainant"] || 'N/A';
      const submissionDate = application["Submission Date"] || 'N/A';
      const status = application["Status"] || 'Unknown';
      const rowIndex = application["Row Index"] || 0;

      card.innerHTML = `
        <h3>${logBookNo}</h3>
        <div class="details">
          <div class="detail-item">
            <span class="detail-label">Police Station</span>
            <span class="detail-value">${policeStation}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Crime No</span>
            <span class="detail-value">${crimeNo}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Complainant</span>
            <span class="detail-value">${complainant}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Submission Date</span>
            <span class="detail-value">${submissionDate}</span>
          </div>
        </div>
        <div class="status ${status.toLowerCase()}">${status}</div>
        ${status === 'Draft' ? `<button class='btn btn-primary' style='margin-top:10px;float:right;' onclick='event.stopPropagation(); handleEditDraft(${rowIndex});'>Edit</button>` : ''}
      `;
      card.onclick = () => handleViewApplication(rowIndex);
      return card;
    }

    function handleBackToDashboard() {
      google.script.host.close(); // Close current modal and return to dashboard
    }

    function handleViewApplication(rowIndex) {
      google.script.run
        .withSuccessHandler(function() {
          google.script.run.showPreview(rowIndex);
        })
        .withFailureHandler(function(error) {
          showError('Failed to view application: ' + error);
        })
        .viewApplication(rowIndex);
    }

    function handleEditDraft(rowIndex) {
      // Store the row index in sessionStorage for InputForm to load
      sessionStorage.setItem('editRowIndex', rowIndex);
      google.script.run.showInputForm(rowIndex);
    }

    function displayApplications(applications) {
      console.log('displayApplications called with:', applications);
      console.log('Applications type:', typeof applications);
      console.log('Applications is array:', Array.isArray(applications));
      console.log('Applications length:', applications ? applications.length : 'N/A');

      applicationsGrid.innerHTML = '';

      // Handle null or undefined applications
      if (!applications || !Array.isArray(applications) || applications.length === 0) {
        console.log('No applications to display - showing empty state');
        applicationsGrid.innerHTML = `
          <div class="empty-state">
            <h2>No Applications Found</h2>
            <p>There are no applications with the selected status.</p>
          </div>
        `;
        return;
      }

      console.log(`Displaying ${applications.length} applications`);
      applications.forEach((app, index) => {
        console.log(`Processing application ${index}:`, app);
        try {
          const card = createApplicationCard(app);
          applicationsGrid.appendChild(card);
        } catch (error) {
          console.error(`Error creating card for application ${index}:`, error);
        }
      });
    }

    // On window load, fetch filtered applications
    window.onload = function() {
      const status = sessionStorage.getItem('filterStatus');
      if (!status) {
        showMessageBox('No filter status specified. Returning to dashboard.', () => {
          google.script.run.onOpen();
        });
        return;
      }

      if (statusBadge) {
        statusBadge.textContent = status;
      }
      showLoader();
      // First, test basic function
      console.log('Testing basic function...');
      google.script.run
        .withSuccessHandler(function(basicResult) {
          console.log('Basic function result:', basicResult);

          // Now run diagnostic
          console.log('Running sheet access diagnosis...');
          google.script.run
            .withSuccessHandler(function(diagnosis) {
              console.log('Sheet diagnosis result:', diagnosis);

              // Now try the actual function
              console.log('Now calling getFilteredApplications...');
              google.script.run
                .withSuccessHandler(function(applications) {
                  console.log('Received applications:', applications);
                  // Handle null response by converting to empty array
                  if (applications === null || applications === undefined) {
                    console.warn('Received null/undefined applications, converting to empty array');
                    applications = [];
                  }
                  displayApplications(applications);
                  hideLoader();
                })
                .withFailureHandler(function(error) {
                  console.error('Error fetching applications:', error);
                  hideLoader();
                  displayApplications([]); // Show empty state
                  showMessageBox('Failed to load applications: ' + (error.message || error));
                })
                .getFilteredApplications(status);
            })
            .withFailureHandler(function(error) {
              console.error('Error in diagnosis:', error);
              hideLoader();
              showMessageBox('Diagnosis failed: ' + (error.message || error));
            })
            .diagnoseSheetAccess();
        })
        .withFailureHandler(function(error) {
          console.error('Error in basic function:', error);
          hideLoader();
          showMessageBox('Basic function failed: ' + (error.message || error));
        })
        .testBasicFunction();
    };
  </script>
</body>
</html> 