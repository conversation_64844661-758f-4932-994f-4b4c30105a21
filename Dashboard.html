<!DOCTYPE html>
<html>
<head>
  <base target="_top">
  <title>CDR Applications Dashboard</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* General styles for the entire page */
    body {
      font-family: 'Inter', sans-serif; /* Using Inter for a modern look */
      margin: 0;
      padding: 0;
      background-color: #f0f2f5; /* Light gray background */
      color: #333; /* Darker text color */
      display: flex;
      flex-direction: column;
      align-items: center; /* Center content horizontally */
      justify-content: flex-start; /* Align content to the top */
      min-height: 100vh; /* Ensure it covers full viewport height */
      overflow-y: auto; /* Allow scrolling if content overflows */
      box-sizing: border-box;
    }

    /* Main container for the dashboard content */
    .dashboard-container {
      max-width: 1200px; /* Max width for larger screens */
      width: 95%; /* Responsive width */
      margin: 30px auto; /* Margin top/bottom, auto left/right for centering */
      background-color: #ffffff; /* White background for the main content area */
      border-radius: 12px; /* Rounded corners for the container */
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1); /* Soft, subtle shadow */
      padding: 30px;
      box-sizing: border-box;
    }

    /* Header styling */
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40px;
      padding-bottom: 20px;
      border-bottom: 1px solid #e0e0e0; /* Subtle separator */
    }

    .header h1 {
      color: #1a73e8; /* Google Blue for the main title */
      font-size: 2.2rem;
      font-weight: 700;
      margin: 0;
    }

    /* New Application Button */
    .btn-new-application {
      background-color: #34a853; /* Google Green */
      color: white;
      padding: 12px 25px;
      border: none;
      border-radius: 8px; /* Rounded button corners */
      font-size: 1.1rem;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.2s ease;
      box-shadow: 0 4px 12px rgba(52, 168, 83, 0.3); /* Green shadow */
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .btn-new-application:hover {
      background-color: #2c8c4a; /* Darker green on hover */
      transform: translateY(-2px); /* Slight lift effect */
      box-shadow: 0 6px 16px rgba(52, 168, 83, 0.4);
    }

    .btn-new-application svg {
      width: 20px;
      height: 20px;
      fill: currentColor; /* Inherit color from text */
    }

    /* Status Cards Grid */
    .status-cards-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Responsive grid */
      gap: 25px; /* Space between cards */
    }

    /* Individual Status Card */
    .status-card {
      background-color: #fdfdfd; /* Slightly off-white card background */
      border: 1px solid #e0e0e0; /* Light border */
      border-radius: 10px; /* Rounded card corners */
      padding: 25px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); /* Soft card shadow */
      transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 150px; /* Fixed height for consistency */
    }

    .status-card:hover {
      transform: translateY(-5px); /* Lift effect on hover */
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); /* More pronounced shadow */
      background-color: #f8f8f8; /* Slightly darker on hover */
    }

    .status-card h3 {
      font-size: 1.4rem;
      font-weight: 600;
      margin-top: 0;
      margin-bottom: 15px;
      color: #555; /* Darker grey for card titles */
    }

    .status-card .count {
      font-size: 3.5rem; /* Large font size for counts */
      font-weight: 700;
      color: #1a73e8; /* Google Blue for counts */
      line-height: 1; /* Adjust line height to prevent extra space */
      margin-bottom: 0;
      transition: color 0.3s ease;
    }

    /* Specific colors for counts */
    .status-card.all-submitted .count { color: #1a73e8; } /* Blue */
    .status-card.pending .count { color: #f4b400; }     /* Google Yellow */
    .status-card.approved .count { color: #34a853; }     /* Google Green */
    .status-card.rejected .count { color: #ea4335; }     /* Google Red */
    .status-card.delivered .count { color: #4285f4; }    /* Google Blue (distinct shade) */
    .status-card.draft .count { color: #9e9e9e; }        /* Grey */

    /* Loader and Message Box (re-used from InputForm) */
    .loader-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      visibility: hidden;
      opacity: 0;
      transition: visibility 0s, opacity 0.3s linear;
    }

    .loader-overlay.show {
      visibility: visible;
      opacity: 1;
    }

    .spinner {
      border: 8px solid #f3f3f3;
      border-top: 8px solid #1a73e8;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .message-box {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: #ffffff;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
      z-index: 1001;
      text-align: center;
      min-width: 300px;
      max-width: 90%;
      visibility: hidden;
      opacity: 0;
      transition: visibility 0s, opacity 0.3s linear;
    }

    .message-box.show {
      visibility: visible;
      opacity: 1;
    }

    .message-box p {
      font-size: 1.1rem;
      margin-bottom: 20px;
      color: #333;
    }

    .message-box button {
      background-color: #1a73e8;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 1rem;
      transition: background-color 0.3s ease;
    }

    .message-box button:hover {
      background-color: #1764cf;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .dashboard-container {
        margin: 15px auto;
        padding: 20px;
      }

      .header {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 30px;
      }

      .header h1 {
        font-size: 1.8rem;
        margin-bottom: 15px;
      }

      .btn-new-application {
        width: 100%;
        justify-content: center;
      }

      .status-cards-grid {
        grid-template-columns: 1fr; /* Single column on small screens */
        gap: 15px;
      }

      .status-card {
        padding: 20px;
        height: auto; /* Allow height to adjust */
      }

      .status-card .count {
        font-size: 3rem;
      }
    }
  </style>
</head>
<body>
  <div class="loader-overlay" id="loader">
    <div class="spinner"></div>
  </div>

  <div class="message-box" id="messageBox">
    <p id="messageText"></p>
    <button id="messageBoxClose">OK</button>
  </div>

  <div class="dashboard-container">
    <div class="header">
      <h1>CDR Applications Dashboard</h1>
      <button class="btn-new-application" onclick="openInputForm()">
        <svg viewBox="0 0 24 24" width="24" height="24" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="white"/>
        </svg>
        New Application
      </button>
    </div>

    <div class="status-cards-grid" id="statusCardsGrid">
      <!-- Cards will be dynamically populated here by JavaScript -->
    </div>
  </div>

  <script>
    // Custom Message Box and Loader functions (re-used for consistency)
    const loader = document.getElementById('loader');
    const messageBox = document.getElementById('messageBox');
    const messageText = document.getElementById('messageText');
    const messageBoxClose = document.getElementById('messageBoxClose');

    function showLoader() {
      loader.classList.add('show');
      console.log('Loader shown.');
    }

    function hideLoader() {
      loader.classList.remove('show');
      console.log('Loader hidden.');
    }

    function showMessageBox(message, callback) {
      messageText.textContent = message;
      messageBox.classList.add('show');
      console.log('Message box shown: ' + message);
      messageBoxClose.onclick = () => {
        hideMessageBox();
        if (callback) callback();
      };
    }

    function hideMessageBox() {
      messageBox.classList.remove('show');
      console.log('Message box hidden.');
    }

    /**
     * Centralized error handler for Apps Script calls.
     * @param {Error} error - The error object.
     */
    function onFailure(error) {
        hideLoader();
        console.error("Error from Apps Script:", error);
        showMessageBox('An error occurred: ' + (error.message || error));
    }

    /**
     * Dynamically creates and populates status cards.
     * @param {object} counts - Object containing counts for different statuses.
     */
    function createStatusCards(counts) {
      const grid = document.getElementById('statusCardsGrid');
      grid.innerHTML = ''; // Clear existing cards

      const cardData = [
        { title: 'All Submitted', count: counts.allSubmitted, status: 'AllSubmitted', className: 'all-submitted' },
        { title: 'Pending', count: counts.pending, status: 'Pending', className: 'pending' },
        { title: 'Approved', count: counts.approved, status: 'Approved', className: 'approved' },
        { title: 'Rejected', count: counts.rejected, status: 'Rejected', className: 'rejected' },
        { title: 'Delivered', count: counts.delivered, status: 'Delivered', className: 'delivered' },
        { title: 'Drafts', count: counts.draft, status: 'Draft', className: 'draft' }
      ];

      cardData.forEach(data => {
        const card = document.createElement('div');
        card.className = `status-card ${data.className}`;
        card.innerHTML = `
          <h3>${data.title}</h3>
          <p class="count">${data.count}</p>
        `;
        card.onclick = () => openFilteredList(data.status); // Attach click handler
        grid.appendChild(card);
      });
      console.log('Status cards populated.');
    }

    /**
     * Opens the Input Form for a new application.
     */
    function openInputForm() {
      console.log('Opening new application form...');
      // Clear any existing edit context from sessionStorage
      sessionStorage.removeItem('editRowIndex');
      sessionStorage.removeItem('applicationData');
      sessionStorage.removeItem('officerSignatureBase64');
      sessionStorage.removeItem('officeSealBase64');
      sessionStorage.removeItem('officerSignatureID');
      sessionStorage.removeItem('officeSealID');
      showLoader();
      google.script.run
        .withSuccessHandler(() => {
          // The showInputForm function opens a new modal.
          // This dashboard remains open underneath, and the user will see InputForm.
          console.log('Input form requested successfully.');
          hideLoader(); // Hide loader after the new modal is triggered
        })
        .withFailureHandler(onFailure)
        .showInputForm();
    }

    /**
     * Opens a filtered list of applications based on status.
     * @param {string} status - The status to filter by.
     */
    function openFilteredList(status) {
      console.log(`Opening filtered list for status: ${status}`);
      sessionStorage.setItem('filterStatus', status); // Store filter status for the list page
      showLoader();
      google.script.run
        .withSuccessHandler(() => {
          console.log(`Filtered applications list for ${status} requested successfully.`);
          hideLoader(); // Hide loader after the new modal is triggered
        })
        .withFailureHandler(onFailure)
        .showFilteredApplicationsList(status); // Call backend to open FilteredApplicationsList.html
    }

    // On window load, fetch dashboard data
    window.onload = function() {
      console.log('Dashboard.html loaded. Fetching data...');
      showLoader();
      google.script.run
        .withSuccessHandler(function(counts) {
          console.log('Dashboard data received:', counts);
          createStatusCards(counts);
          hideLoader();
        })
        .withFailureHandler(onFailure)
        .getDashboardData();
    };
  </script>
</body>
</html>
