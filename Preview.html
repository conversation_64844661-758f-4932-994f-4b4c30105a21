<!DOCTYPE html>
<html>
<head>
  <base target="_top">
  <title>Application Preview</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* General styles */
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f2f5;
      color: #333;
      display: flex;
      flex-direction: column;
      height: 100vh;
      overflow: hidden; /* Prevent body scrolling */
    }

    h1 {
      color: #1a73e8;
      text-align: center;
      margin-bottom: 25px;
    }

    /* Container for content */
    .preview-container {
      flex-grow: 1; /* Allow content to fill available height */
      display: flex;
      flex-direction: column;
      max-width: 900px;
      margin: 0 auto;
      background-color: #ffffff;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      padding: 30px;
      box-sizing: border-box;
      overflow: hidden; /* For inner scrolling of iframe */
    }

    /* PDF Frame */
    .pdf-frame-wrapper {
      flex-grow: 1; /* Allow iframe to fill available height */
      border: 1px solid #ddd;
      border-radius: 8px;
      overflow: hidden; /* Hide scrollbars if content fits */
      margin-bottom: 20px;
      background-color: #e0e0e0; /* Placeholder background */
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
    }

    .pdf-frame-wrapper iframe {
      width: 100%;
      height: 100%;
      border: none;
      display: block; /* Remove extra space below iframe */
    }

    .pdf-loading-message {
        position: absolute;
        color: #666;
        font-size: 1.1rem;
        text-align: center;
    }

    /* Action Buttons */
    .preview-actions {
      display: flex;
      justify-content: center;
      gap: 20px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }

    .btn {
      padding: 12px 25px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 500;
      transition: background-color 0.3s ease, transform 0.2s ease;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .btn-edit {
      background-color: #fbbc05; /* Google Yellow */
      color: #333;
      box-shadow: 0 4px 10px rgba(251, 188, 5, 0.3);
    }

    .btn-edit:hover {
      background-color: #e6a700;
      transform: translateY(-2px);
    }

    .btn-submit {
      background-color: #34a853; /* Google Green */
      color: white;
      box-shadow: 0 4px 10px rgba(52, 168, 83, 0.3);
    }

    .btn-submit:hover {
      background-color: #2b8e45;
      transform: translateY(-2px);
    }

    /* Loader and Message Box (re-defined for this HTML file) */
    .loader-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      visibility: hidden;
      opacity: 0;
      transition: visibility 0s, opacity 0.3s linear;
    }

    .loader-overlay.show {
      visibility: visible;
      opacity: 1;
    }

    .spinner {
      border: 8px solid #f3f3f3;
      border-top: 8px solid #1a73e8;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .message-box {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: #ffffff;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
      z-index: 1001;
      text-align: center;
      min-width: 300px;
      max-width: 90%;
      visibility: hidden;
      opacity: 0;
      transition: visibility 0s, opacity 0.3s linear;
    }

    .message-box.show {
      visibility: visible;
      opacity: 1;
    }

    .message-box p {
      font-size: 1.1rem;
      margin-bottom: 20px;
      color: #333;
    }

    .message-box button {
      background-color: #1a73e8;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 1rem;
      transition: background-color 0.3s ease;
    }

    .message-box button:hover {
      background-color: #1764cf;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      body {
        padding: 15px;
      }
      .preview-container {
        padding: 20px;
      }
      .preview-actions {
        flex-direction: column;
        gap: 10px;
      }
      .btn {
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="loader-overlay" id="loader">
    <div class="spinner"></div>
  </div>

  <div class="message-box" id="messageBox">
    <p id="messageText"></p>
    <button id="messageBoxClose">OK</button>
  </div>

  <div class="preview-container">
    <!-- Header -->
    <div class="header">
      <h1>Application Preview</h1>
      <div class="header-actions">
        <button type="button" class="btn btn-secondary" onclick="handleEdit()">Edit Application</button>
        <button type="button" class="btn btn-primary" onclick="handleSubmitAndSend()">Submit and Send</button>
      </div>
    </div>
    <div class="pdf-frame-wrapper">
      <iframe id="pdfFrame" src=""></iframe>
      <div class="pdf-loading-message" id="pdfLoadingMessage">Generating PDF preview...</div>
    </div>
    <div class="preview-actions">
      <button class="btn btn-edit" onclick="editApplication()">Edit Application</button>
      <button class="btn btn-submit" onclick="submitApplicationConfirm()">Submit & Send</button>
    </div>
  </div>

  <script>
    // Custom Message Box and Loader functions
    const loader = document.getElementById('loader');
    const messageBox = document.getElementById('messageBox');
    const messageText = document.getElementById('messageText');
    const messageBoxClose = document.getElementById('messageBoxClose');
    const pdfLoadingMessage = document.getElementById('pdfLoadingMessage');

    function showLoader() {
      loader.classList.add('show');
    }

    function hideLoader() {
      loader.classList.remove('show');
    }

    function showMessageBox(message, callback) {
      messageText.textContent = message;
      messageBox.classList.add('show');
      messageBoxClose.onclick = () => {
        hideMessageBox();
        if (callback) callback();
      };
    }

    function hideMessageBox() {
      messageBox.classList.remove('show');
    }

    let applicationData;
    let applicationRowIndex;

    /**
     * Handles success callbacks from google.script.run for PDF generation.
     * @param {object} result - The result from the server function.
     */
    function onPdfSuccess(result) {
      hideLoader();
      pdfLoadingMessage.style.display = 'none'; // Hide loading message
      if (result.success) {
        // Set src to the Base64 encoded PDF string
        document.getElementById('pdfFrame').src = "data:application/pdf;base64," + result.base64Pdf;
      } else {
        showMessageBox('Failed to generate PDF preview: ' + (result.message || 'Unknown error.'));
        console.error('PDF generation error:', result.message);
      }
    }

    /**
     * Handles success callbacks from google.script.run for submission.
     * @param {object} result - The result from the server function.
     */
    function onSubmitSuccess(result) {
      hideLoader();
      if (result.success) {
        showMessageBox(result.message, () => {
          sessionStorage.clear(); // Clear all session storage related to the application
          google.script.host.close(); // Close the modal
        });
      } else {
        showMessageBox('Failed to submit application: ' + (result.message || 'Unknown error.'));
      }
    }

    /**
     * Handles failure callbacks from google.script.run.
     * @param {Error} error - The error object.
     */
    function onFailure(error) {
      hideLoader();
      pdfLoadingMessage.style.display = 'none'; // Hide loading message
      console.error("Server error:", error);
      showMessageBox('Error: ' + error.message);
    }

    /**
     * Edits the current application by returning to the input form.
     */
    function editApplication() {
      // applicationData and applicationRowIndex are already in sessionStorage from InputForm
      // We just need to close this Preview modal. InputForm will automatically re-render its state
      // based on the presence of 'editRowIndex' in sessionStorage.
      showLoader();
      google.script.host.close(); // Close the Preview modal
      // The InputForm will still be open underneath, or the onOpen for the sheet will reload the dashboard
    }

    /**
     * Confirms with user before submitting the application and sending the email.
     */
    function submitApplicationConfirm() {
      if (!applicationData || !applicationRowIndex) {
        showMessageBox('Application data not found. Please go back and save the application first.');
        return;
      }

      // Use custom message box for confirmation
      const confirmMessage = "Are you sure you want to submit this application? This action cannot be undone.";
      messageText.textContent = confirmMessage;

      const confirmButton = document.createElement('button');
      confirmButton.textContent = 'Yes, Submit';
      confirmButton.className = 'btn-submit'; // Apply submit button styling
      confirmButton.style.marginRight = '10px';

      const cancelButton = document.createElement('button');
      cancelButton.textContent = 'No, Cancel';
      cancelButton.className = 'btn-cancel'; // Apply cancel button styling

      messageBox.querySelector('p').innerHTML = ''; // Clear previous message
      messageBox.querySelector('p').appendChild(document.createTextNode(confirmMessage));
      messageBox.appendChild(confirmButton);
      messageBox.appendChild(cancelButton);
      messageBox.classList.add('show');

      confirmButton.onclick = () => {
        hideMessageBox();
        confirmButton.remove(); // Remove buttons to clean up for next message
        cancelButton.remove();
        submitApplication(); // Proceed with submission
      };

      cancelButton.onclick = () => {
        hideMessageBox();
        confirmButton.remove(); // Remove buttons
        cancelButton.remove();
      };
    }

    /**
     * Submits the application and sends the email (called after confirmation).
     */
    function submitApplication() {
        showLoader();
        google.script.run
            .withSuccessHandler(onSubmitSuccess)
            .withFailureHandler(onFailure)
            .submitApplication(applicationData, applicationRowIndex);
    }

    // On window load, retrieve data and generate PDF preview
    window.onload = function() {
      const storedData = sessionStorage.getItem('applicationData');
      const storedRowIndex = sessionStorage.getItem('editRowIndex');

      if (storedData && storedRowIndex) {
        applicationData = JSON.parse(storedData);
        applicationRowIndex = parseInt(storedRowIndex);

        // Ensure image IDs are part of applicationData for PDF generation if they exist in sessionStorage
        // These would have been passed from InputForm if they exist
        applicationData.officerSignatureID = sessionStorage.getItem('officerSignatureID') || applicationData.officerSignatureID;
        applicationData.officeSealID = sessionStorage.getItem('officeSealID') || applicationData.officeSealID;

        showLoader();
        pdfLoadingMessage.style.display = 'block'; // Show loading message
        google.script.run
          .withSuccessHandler(onPdfSuccess)
          .withFailureHandler(onFailure)
          .generatePdfPreview(applicationData);
      } else {
        showMessageBox('No application data found. Please go back to the form.', () => {
            // Optionally, close the modal if no data is found and direct the user
            google.script.host.close();
            // It might be good to open the dashboard here, but the prompt doesn't explicitly asks for it.
            // google.script.run.closeFormAndShowDashboard(); // Could add this for better UX
        });
        console.error('No applicationData or editRowIndex in sessionStorage.');
      }
    };

    function handleEdit() {
      // Get the row index from sessionStorage or context
      const rowIndex = sessionStorage.getItem('editRowIndex');
      google.script.run.showInputForm(rowIndex);
    }

    function handleSubmitAndSend() {
      // Get the row index from sessionStorage or context
      const rowIndex = sessionStorage.getItem('editRowIndex');
      showLoader();
      google.script.run
        .withSuccessHandler(function(response) {
          hideLoader();
          if (response.success) {
            showMessageBox('Application submitted and sent successfully!', function() {
              google.script.run.showDashboard();
            });
          } else {
            showMessageBox('Failed to submit and send: ' + response.message);
          }
        })
        .withFailureHandler(function(error) {
          hideLoader();
          showMessageBox('Failed to submit and send: ' + error);
        })
        .submitAndSendApplication(rowIndex);
    }
  </script>
</body>
</html>
